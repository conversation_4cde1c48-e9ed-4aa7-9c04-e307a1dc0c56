{"version": 3, "sources": ["../../pinia/dist/pinia.mjs"], "sourcesContent": ["/*!\n * pinia v2.3.1\n * (c) 2025 <PERSON>\n * @license MIT\n */\nimport { hasInjectionContext, inject, toRaw, watch, unref, markRaw, effectScope, ref, isVue2, isRef, isReactive, set, getCurrentScope, onScopeDispose, getCurrentInstance, reactive, toRef, del, nextTick, computed, toRefs } from 'vue-demi';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * setActivePinia must be called to handle SSR at the top of functions like\n * `fetch`, `setup`, `serverPrefetch` and others\n */\nlet activePinia;\n/**\n * Sets or unsets the active pinia. Used in SSR and internally when calling\n * actions and getters\n *\n * @param pinia - Pinia instance\n */\n// @ts-expect-error: cannot constrain the type of the return\nconst setActivePinia = (pinia) => (activePinia = pinia);\n/**\n * Get the currently active pinia if there is any.\n */\nconst getActivePinia = () => (hasInjectionContext() && inject(piniaSymbol)) || activePinia;\nconst piniaSymbol = ((process.env.NODE_ENV !== 'production') ? Symbol('pinia') : /* istanbul ignore next */ Symbol());\n\nfunction isPlainObject(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\no) {\n    return (o &&\n        typeof o === 'object' &&\n        Object.prototype.toString.call(o) === '[object Object]' &&\n        typeof o.toJSON !== 'function');\n}\n// type DeepReadonly<T> = { readonly [P in keyof T]: DeepReadonly<T[P]> }\n// TODO: can we change these to numbers?\n/**\n * Possible types for SubscriptionCallback\n */\nvar MutationType;\n(function (MutationType) {\n    /**\n     * Direct mutation of the state:\n     *\n     * - `store.name = 'new name'`\n     * - `store.$state.name = 'new name'`\n     * - `store.list.push('new item')`\n     */\n    MutationType[\"direct\"] = \"direct\";\n    /**\n     * Mutated the state with `$patch` and an object\n     *\n     * - `store.$patch({ name: 'newName' })`\n     */\n    MutationType[\"patchObject\"] = \"patch object\";\n    /**\n     * Mutated the state with `$patch` and a function\n     *\n     * - `store.$patch(state => state.name = 'newName')`\n     */\n    MutationType[\"patchFunction\"] = \"patch function\";\n    // maybe reset? for $state = {} and $reset\n})(MutationType || (MutationType = {}));\n\nconst IS_CLIENT = typeof window !== 'undefined';\n\n/*\n * FileSaver.js A saveAs() FileSaver implementation.\n *\n * Originally by Eli Grey, adapted as an ESM module by Eduardo San Martin\n * Morote.\n *\n * License : MIT\n */\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nconst _global = /*#__PURE__*/ (() => typeof window === 'object' && window.window === window\n    ? window\n    : typeof self === 'object' && self.self === self\n        ? self\n        : typeof global === 'object' && global.global === global\n            ? global\n            : typeof globalThis === 'object'\n                ? globalThis\n                : { HTMLElement: null })();\nfunction bom(blob, { autoBom = false } = {}) {\n    // prepend BOM for UTF-8 XML and text/* types (including HTML)\n    // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n    if (autoBom &&\n        /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n        return new Blob([String.fromCharCode(0xfeff), blob], { type: blob.type });\n    }\n    return blob;\n}\nfunction download(url, name, opts) {\n    const xhr = new XMLHttpRequest();\n    xhr.open('GET', url);\n    xhr.responseType = 'blob';\n    xhr.onload = function () {\n        saveAs(xhr.response, name, opts);\n    };\n    xhr.onerror = function () {\n        console.error('could not download file');\n    };\n    xhr.send();\n}\nfunction corsEnabled(url) {\n    const xhr = new XMLHttpRequest();\n    // use sync to avoid popup blocker\n    xhr.open('HEAD', url, false);\n    try {\n        xhr.send();\n    }\n    catch (e) { }\n    return xhr.status >= 200 && xhr.status <= 299;\n}\n// `a.click()` doesn't work for all browsers (#465)\nfunction click(node) {\n    try {\n        node.dispatchEvent(new MouseEvent('click'));\n    }\n    catch (e) {\n        const evt = document.createEvent('MouseEvents');\n        evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);\n        node.dispatchEvent(evt);\n    }\n}\nconst _navigator = typeof navigator === 'object' ? navigator : { userAgent: '' };\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nconst isMacOSWebView = /*#__PURE__*/ (() => /Macintosh/.test(_navigator.userAgent) &&\n    /AppleWebKit/.test(_navigator.userAgent) &&\n    !/Safari/.test(_navigator.userAgent))();\nconst saveAs = !IS_CLIENT\n    ? () => { } // noop\n    : // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView or mini program\n        typeof HTMLAnchorElement !== 'undefined' &&\n            'download' in HTMLAnchorElement.prototype &&\n            !isMacOSWebView\n            ? downloadSaveAs\n            : // Use msSaveOrOpenBlob as a second approach\n                'msSaveOrOpenBlob' in _navigator\n                    ? msSaveAs\n                    : // Fallback to using FileReader and a popup\n                        fileSaverSaveAs;\nfunction downloadSaveAs(blob, name = 'download', opts) {\n    const a = document.createElement('a');\n    a.download = name;\n    a.rel = 'noopener'; // tabnabbing\n    // TODO: detect chrome extensions & packaged apps\n    // a.target = '_blank'\n    if (typeof blob === 'string') {\n        // Support regular links\n        a.href = blob;\n        if (a.origin !== location.origin) {\n            if (corsEnabled(a.href)) {\n                download(blob, name, opts);\n            }\n            else {\n                a.target = '_blank';\n                click(a);\n            }\n        }\n        else {\n            click(a);\n        }\n    }\n    else {\n        // Support blobs\n        a.href = URL.createObjectURL(blob);\n        setTimeout(function () {\n            URL.revokeObjectURL(a.href);\n        }, 4e4); // 40s\n        setTimeout(function () {\n            click(a);\n        }, 0);\n    }\n}\nfunction msSaveAs(blob, name = 'download', opts) {\n    if (typeof blob === 'string') {\n        if (corsEnabled(blob)) {\n            download(blob, name, opts);\n        }\n        else {\n            const a = document.createElement('a');\n            a.href = blob;\n            a.target = '_blank';\n            setTimeout(function () {\n                click(a);\n            });\n        }\n    }\n    else {\n        // @ts-ignore: works on windows\n        navigator.msSaveOrOpenBlob(bom(blob, opts), name);\n    }\n}\nfunction fileSaverSaveAs(blob, name, opts, popup) {\n    // Open a popup immediately do go around popup blocker\n    // Mostly only available on user interaction and the fileReader is async so...\n    popup = popup || open('', '_blank');\n    if (popup) {\n        popup.document.title = popup.document.body.innerText = 'downloading...';\n    }\n    if (typeof blob === 'string')\n        return download(blob, name, opts);\n    const force = blob.type === 'application/octet-stream';\n    const isSafari = /constructor/i.test(String(_global.HTMLElement)) || 'safari' in _global;\n    const isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) &&\n        typeof FileReader !== 'undefined') {\n        // Safari doesn't allow downloading of blob URLs\n        const reader = new FileReader();\n        reader.onloadend = function () {\n            let url = reader.result;\n            if (typeof url !== 'string') {\n                popup = null;\n                throw new Error('Wrong reader.result type');\n            }\n            url = isChromeIOS\n                ? url\n                : url.replace(/^data:[^;]*;/, 'data:attachment/file;');\n            if (popup) {\n                popup.location.href = url;\n            }\n            else {\n                location.assign(url);\n            }\n            popup = null; // reverse-tabnabbing #460\n        };\n        reader.readAsDataURL(blob);\n    }\n    else {\n        const url = URL.createObjectURL(blob);\n        if (popup)\n            popup.location.assign(url);\n        else\n            location.href = url;\n        popup = null; // reverse-tabnabbing #460\n        setTimeout(function () {\n            URL.revokeObjectURL(url);\n        }, 4e4); // 40s\n    }\n}\n\n/**\n * Shows a toast or console.log\n *\n * @param message - message to log\n * @param type - different color of the tooltip\n */\nfunction toastMessage(message, type) {\n    const piniaMessage = '🍍 ' + message;\n    if (typeof __VUE_DEVTOOLS_TOAST__ === 'function') {\n        // No longer available :(\n        __VUE_DEVTOOLS_TOAST__(piniaMessage, type);\n    }\n    else if (type === 'error') {\n        console.error(piniaMessage);\n    }\n    else if (type === 'warn') {\n        console.warn(piniaMessage);\n    }\n    else {\n        console.log(piniaMessage);\n    }\n}\nfunction isPinia(o) {\n    return '_a' in o && 'install' in o;\n}\n\n/**\n * This file contain devtools actions, they are not Pinia actions.\n */\n// ---\nfunction checkClipboardAccess() {\n    if (!('clipboard' in navigator)) {\n        toastMessage(`Your browser doesn't support the Clipboard API`, 'error');\n        return true;\n    }\n}\nfunction checkNotFocusedError(error) {\n    if (error instanceof Error &&\n        error.message.toLowerCase().includes('document is not focused')) {\n        toastMessage('You need to activate the \"Emulate a focused page\" setting in the \"Rendering\" panel of devtools.', 'warn');\n        return true;\n    }\n    return false;\n}\nasync function actionGlobalCopyState(pinia) {\n    if (checkClipboardAccess())\n        return;\n    try {\n        await navigator.clipboard.writeText(JSON.stringify(pinia.state.value));\n        toastMessage('Global state copied to clipboard.');\n    }\n    catch (error) {\n        if (checkNotFocusedError(error))\n            return;\n        toastMessage(`Failed to serialize the state. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nasync function actionGlobalPasteState(pinia) {\n    if (checkClipboardAccess())\n        return;\n    try {\n        loadStoresState(pinia, JSON.parse(await navigator.clipboard.readText()));\n        toastMessage('Global state pasted from clipboard.');\n    }\n    catch (error) {\n        if (checkNotFocusedError(error))\n            return;\n        toastMessage(`Failed to deserialize the state from clipboard. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nasync function actionGlobalSaveState(pinia) {\n    try {\n        saveAs(new Blob([JSON.stringify(pinia.state.value)], {\n            type: 'text/plain;charset=utf-8',\n        }), 'pinia-state.json');\n    }\n    catch (error) {\n        toastMessage(`Failed to export the state as JSON. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nlet fileInput;\nfunction getFileOpener() {\n    if (!fileInput) {\n        fileInput = document.createElement('input');\n        fileInput.type = 'file';\n        fileInput.accept = '.json';\n    }\n    function openFile() {\n        return new Promise((resolve, reject) => {\n            fileInput.onchange = async () => {\n                const files = fileInput.files;\n                if (!files)\n                    return resolve(null);\n                const file = files.item(0);\n                if (!file)\n                    return resolve(null);\n                return resolve({ text: await file.text(), file });\n            };\n            // @ts-ignore: TODO: changed from 4.3 to 4.4\n            fileInput.oncancel = () => resolve(null);\n            fileInput.onerror = reject;\n            fileInput.click();\n        });\n    }\n    return openFile;\n}\nasync function actionGlobalOpenStateFile(pinia) {\n    try {\n        const open = getFileOpener();\n        const result = await open();\n        if (!result)\n            return;\n        const { text, file } = result;\n        loadStoresState(pinia, JSON.parse(text));\n        toastMessage(`Global state imported from \"${file.name}\".`);\n    }\n    catch (error) {\n        toastMessage(`Failed to import the state from JSON. Check the console for more details.`, 'error');\n        console.error(error);\n    }\n}\nfunction loadStoresState(pinia, state) {\n    for (const key in state) {\n        const storeState = pinia.state.value[key];\n        // store is already instantiated, patch it\n        if (storeState) {\n            Object.assign(storeState, state[key]);\n        }\n        else {\n            // store is not instantiated, set the initial state\n            pinia.state.value[key] = state[key];\n        }\n    }\n}\n\nfunction formatDisplay(display) {\n    return {\n        _custom: {\n            display,\n        },\n    };\n}\nconst PINIA_ROOT_LABEL = '🍍 Pinia (root)';\nconst PINIA_ROOT_ID = '_root';\nfunction formatStoreForInspectorTree(store) {\n    return isPinia(store)\n        ? {\n            id: PINIA_ROOT_ID,\n            label: PINIA_ROOT_LABEL,\n        }\n        : {\n            id: store.$id,\n            label: store.$id,\n        };\n}\nfunction formatStoreForInspectorState(store) {\n    if (isPinia(store)) {\n        const storeNames = Array.from(store._s.keys());\n        const storeMap = store._s;\n        const state = {\n            state: storeNames.map((storeId) => ({\n                editable: true,\n                key: storeId,\n                value: store.state.value[storeId],\n            })),\n            getters: storeNames\n                .filter((id) => storeMap.get(id)._getters)\n                .map((id) => {\n                const store = storeMap.get(id);\n                return {\n                    editable: false,\n                    key: id,\n                    value: store._getters.reduce((getters, key) => {\n                        getters[key] = store[key];\n                        return getters;\n                    }, {}),\n                };\n            }),\n        };\n        return state;\n    }\n    const state = {\n        state: Object.keys(store.$state).map((key) => ({\n            editable: true,\n            key,\n            value: store.$state[key],\n        })),\n    };\n    // avoid adding empty getters\n    if (store._getters && store._getters.length) {\n        state.getters = store._getters.map((getterName) => ({\n            editable: false,\n            key: getterName,\n            value: store[getterName],\n        }));\n    }\n    if (store._customProperties.size) {\n        state.customProperties = Array.from(store._customProperties).map((key) => ({\n            editable: true,\n            key,\n            value: store[key],\n        }));\n    }\n    return state;\n}\nfunction formatEventData(events) {\n    if (!events)\n        return {};\n    if (Array.isArray(events)) {\n        // TODO: handle add and delete for arrays and objects\n        return events.reduce((data, event) => {\n            data.keys.push(event.key);\n            data.operations.push(event.type);\n            data.oldValue[event.key] = event.oldValue;\n            data.newValue[event.key] = event.newValue;\n            return data;\n        }, {\n            oldValue: {},\n            keys: [],\n            operations: [],\n            newValue: {},\n        });\n    }\n    else {\n        return {\n            operation: formatDisplay(events.type),\n            key: formatDisplay(events.key),\n            oldValue: events.oldValue,\n            newValue: events.newValue,\n        };\n    }\n}\nfunction formatMutationType(type) {\n    switch (type) {\n        case MutationType.direct:\n            return 'mutation';\n        case MutationType.patchFunction:\n            return '$patch';\n        case MutationType.patchObject:\n            return '$patch';\n        default:\n            return 'unknown';\n    }\n}\n\n// timeline can be paused when directly changing the state\nlet isTimelineActive = true;\nconst componentStateTypes = [];\nconst MUTATIONS_LAYER_ID = 'pinia:mutations';\nconst INSPECTOR_ID = 'pinia';\nconst { assign: assign$1 } = Object;\n/**\n * Gets the displayed name of a store in devtools\n *\n * @param id - id of the store\n * @returns a formatted string\n */\nconst getStoreType = (id) => '🍍 ' + id;\n/**\n * Add the pinia plugin without any store. Allows displaying a Pinia plugin tab\n * as soon as it is added to the application.\n *\n * @param app - Vue application\n * @param pinia - pinia instance\n */\nfunction registerPiniaDevtools(app, pinia) {\n    setupDevtoolsPlugin({\n        id: 'dev.esm.pinia',\n        label: 'Pinia 🍍',\n        logo: 'https://pinia.vuejs.org/logo.svg',\n        packageName: 'pinia',\n        homepage: 'https://pinia.vuejs.org',\n        componentStateTypes,\n        app,\n    }, (api) => {\n        if (typeof api.now !== 'function') {\n            toastMessage('You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n        }\n        api.addTimelineLayer({\n            id: MUTATIONS_LAYER_ID,\n            label: `Pinia 🍍`,\n            color: 0xe5df88,\n        });\n        api.addInspector({\n            id: INSPECTOR_ID,\n            label: 'Pinia 🍍',\n            icon: 'storage',\n            treeFilterPlaceholder: 'Search stores',\n            actions: [\n                {\n                    icon: 'content_copy',\n                    action: () => {\n                        actionGlobalCopyState(pinia);\n                    },\n                    tooltip: 'Serialize and copy the state',\n                },\n                {\n                    icon: 'content_paste',\n                    action: async () => {\n                        await actionGlobalPasteState(pinia);\n                        api.sendInspectorTree(INSPECTOR_ID);\n                        api.sendInspectorState(INSPECTOR_ID);\n                    },\n                    tooltip: 'Replace the state with the content of your clipboard',\n                },\n                {\n                    icon: 'save',\n                    action: () => {\n                        actionGlobalSaveState(pinia);\n                    },\n                    tooltip: 'Save the state as a JSON file',\n                },\n                {\n                    icon: 'folder_open',\n                    action: async () => {\n                        await actionGlobalOpenStateFile(pinia);\n                        api.sendInspectorTree(INSPECTOR_ID);\n                        api.sendInspectorState(INSPECTOR_ID);\n                    },\n                    tooltip: 'Import the state from a JSON file',\n                },\n            ],\n            nodeActions: [\n                {\n                    icon: 'restore',\n                    tooltip: 'Reset the state (with \"$reset\")',\n                    action: (nodeId) => {\n                        const store = pinia._s.get(nodeId);\n                        if (!store) {\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it wasn't found.`, 'warn');\n                        }\n                        else if (typeof store.$reset !== 'function') {\n                            toastMessage(`Cannot reset \"${nodeId}\" store because it doesn't have a \"$reset\" method implemented.`, 'warn');\n                        }\n                        else {\n                            store.$reset();\n                            toastMessage(`Store \"${nodeId}\" reset.`);\n                        }\n                    },\n                },\n            ],\n        });\n        api.on.inspectComponent((payload, ctx) => {\n            const proxy = (payload.componentInstance &&\n                payload.componentInstance.proxy);\n            if (proxy && proxy._pStores) {\n                const piniaStores = payload.componentInstance.proxy._pStores;\n                Object.values(piniaStores).forEach((store) => {\n                    payload.instanceData.state.push({\n                        type: getStoreType(store.$id),\n                        key: 'state',\n                        editable: true,\n                        value: store._isOptionsAPI\n                            ? {\n                                _custom: {\n                                    value: toRaw(store.$state),\n                                    actions: [\n                                        {\n                                            icon: 'restore',\n                                            tooltip: 'Reset the state of this store',\n                                            action: () => store.$reset(),\n                                        },\n                                    ],\n                                },\n                            }\n                            : // NOTE: workaround to unwrap transferred refs\n                                Object.keys(store.$state).reduce((state, key) => {\n                                    state[key] = store.$state[key];\n                                    return state;\n                                }, {}),\n                    });\n                    if (store._getters && store._getters.length) {\n                        payload.instanceData.state.push({\n                            type: getStoreType(store.$id),\n                            key: 'getters',\n                            editable: false,\n                            value: store._getters.reduce((getters, key) => {\n                                try {\n                                    getters[key] = store[key];\n                                }\n                                catch (error) {\n                                    // @ts-expect-error: we just want to show it in devtools\n                                    getters[key] = error;\n                                }\n                                return getters;\n                            }, {}),\n                        });\n                    }\n                });\n            }\n        });\n        api.on.getInspectorTree((payload) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                let stores = [pinia];\n                stores = stores.concat(Array.from(pinia._s.values()));\n                payload.rootNodes = (payload.filter\n                    ? stores.filter((store) => '$id' in store\n                        ? store.$id\n                            .toLowerCase()\n                            .includes(payload.filter.toLowerCase())\n                        : PINIA_ROOT_LABEL.toLowerCase().includes(payload.filter.toLowerCase()))\n                    : stores).map(formatStoreForInspectorTree);\n            }\n        });\n        // Expose pinia instance as $pinia to window\n        globalThis.$pinia = pinia;\n        api.on.getInspectorState((payload) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\n                    ? pinia\n                    : pinia._s.get(payload.nodeId);\n                if (!inspectedStore) {\n                    // this could be the selected store restored for a different project\n                    // so it's better not to say anything here\n                    return;\n                }\n                if (inspectedStore) {\n                    // Expose selected store as $store to window\n                    if (payload.nodeId !== PINIA_ROOT_ID)\n                        globalThis.$store = toRaw(inspectedStore);\n                    payload.state = formatStoreForInspectorState(inspectedStore);\n                }\n            }\n        });\n        api.on.editInspectorState((payload, ctx) => {\n            if (payload.app === app && payload.inspectorId === INSPECTOR_ID) {\n                const inspectedStore = payload.nodeId === PINIA_ROOT_ID\n                    ? pinia\n                    : pinia._s.get(payload.nodeId);\n                if (!inspectedStore) {\n                    return toastMessage(`store \"${payload.nodeId}\" not found`, 'error');\n                }\n                const { path } = payload;\n                if (!isPinia(inspectedStore)) {\n                    // access only the state\n                    if (path.length !== 1 ||\n                        !inspectedStore._customProperties.has(path[0]) ||\n                        path[0] in inspectedStore.$state) {\n                        path.unshift('$state');\n                    }\n                }\n                else {\n                    // Root access, we can omit the `.value` because the devtools API does it for us\n                    path.unshift('state');\n                }\n                isTimelineActive = false;\n                payload.set(inspectedStore, path, payload.state.value);\n                isTimelineActive = true;\n            }\n        });\n        api.on.editComponentState((payload) => {\n            if (payload.type.startsWith('🍍')) {\n                const storeId = payload.type.replace(/^🍍\\s*/, '');\n                const store = pinia._s.get(storeId);\n                if (!store) {\n                    return toastMessage(`store \"${storeId}\" not found`, 'error');\n                }\n                const { path } = payload;\n                if (path[0] !== 'state') {\n                    return toastMessage(`Invalid path for store \"${storeId}\":\\n${path}\\nOnly state can be modified.`);\n                }\n                // rewrite the first entry to be able to directly set the state as\n                // well as any other path\n                path[0] = '$state';\n                isTimelineActive = false;\n                payload.set(store, path, payload.state.value);\n                isTimelineActive = true;\n            }\n        });\n    });\n}\nfunction addStoreToDevtools(app, store) {\n    if (!componentStateTypes.includes(getStoreType(store.$id))) {\n        componentStateTypes.push(getStoreType(store.$id));\n    }\n    setupDevtoolsPlugin({\n        id: 'dev.esm.pinia',\n        label: 'Pinia 🍍',\n        logo: 'https://pinia.vuejs.org/logo.svg',\n        packageName: 'pinia',\n        homepage: 'https://pinia.vuejs.org',\n        componentStateTypes,\n        app,\n        settings: {\n            logStoreChanges: {\n                label: 'Notify about new/deleted stores',\n                type: 'boolean',\n                defaultValue: true,\n            },\n            // useEmojis: {\n            //   label: 'Use emojis in messages ⚡️',\n            //   type: 'boolean',\n            //   defaultValue: true,\n            // },\n        },\n    }, (api) => {\n        // gracefully handle errors\n        const now = typeof api.now === 'function' ? api.now.bind(api) : Date.now;\n        store.$onAction(({ after, onError, name, args }) => {\n            const groupId = runningActionId++;\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: {\n                    time: now(),\n                    title: '🛫 ' + name,\n                    subtitle: 'start',\n                    data: {\n                        store: formatDisplay(store.$id),\n                        action: formatDisplay(name),\n                        args,\n                    },\n                    groupId,\n                },\n            });\n            after((result) => {\n                activeAction = undefined;\n                api.addTimelineEvent({\n                    layerId: MUTATIONS_LAYER_ID,\n                    event: {\n                        time: now(),\n                        title: '🛬 ' + name,\n                        subtitle: 'end',\n                        data: {\n                            store: formatDisplay(store.$id),\n                            action: formatDisplay(name),\n                            args,\n                            result,\n                        },\n                        groupId,\n                    },\n                });\n            });\n            onError((error) => {\n                activeAction = undefined;\n                api.addTimelineEvent({\n                    layerId: MUTATIONS_LAYER_ID,\n                    event: {\n                        time: now(),\n                        logType: 'error',\n                        title: '💥 ' + name,\n                        subtitle: 'end',\n                        data: {\n                            store: formatDisplay(store.$id),\n                            action: formatDisplay(name),\n                            args,\n                            error,\n                        },\n                        groupId,\n                    },\n                });\n            });\n        }, true);\n        store._customProperties.forEach((name) => {\n            watch(() => unref(store[name]), (newValue, oldValue) => {\n                api.notifyComponentUpdate();\n                api.sendInspectorState(INSPECTOR_ID);\n                if (isTimelineActive) {\n                    api.addTimelineEvent({\n                        layerId: MUTATIONS_LAYER_ID,\n                        event: {\n                            time: now(),\n                            title: 'Change',\n                            subtitle: name,\n                            data: {\n                                newValue,\n                                oldValue,\n                            },\n                            groupId: activeAction,\n                        },\n                    });\n                }\n            }, { deep: true });\n        });\n        store.$subscribe(({ events, type }, state) => {\n            api.notifyComponentUpdate();\n            api.sendInspectorState(INSPECTOR_ID);\n            if (!isTimelineActive)\n                return;\n            // rootStore.state[store.id] = state\n            const eventData = {\n                time: now(),\n                title: formatMutationType(type),\n                data: assign$1({ store: formatDisplay(store.$id) }, formatEventData(events)),\n                groupId: activeAction,\n            };\n            if (type === MutationType.patchFunction) {\n                eventData.subtitle = '⤵️';\n            }\n            else if (type === MutationType.patchObject) {\n                eventData.subtitle = '🧩';\n            }\n            else if (events && !Array.isArray(events)) {\n                eventData.subtitle = events.type;\n            }\n            if (events) {\n                eventData.data['rawEvent(s)'] = {\n                    _custom: {\n                        display: 'DebuggerEvent',\n                        type: 'object',\n                        tooltip: 'raw DebuggerEvent[]',\n                        value: events,\n                    },\n                };\n            }\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: eventData,\n            });\n        }, { detached: true, flush: 'sync' });\n        const hotUpdate = store._hotUpdate;\n        store._hotUpdate = markRaw((newStore) => {\n            hotUpdate(newStore);\n            api.addTimelineEvent({\n                layerId: MUTATIONS_LAYER_ID,\n                event: {\n                    time: now(),\n                    title: '🔥 ' + store.$id,\n                    subtitle: 'HMR update',\n                    data: {\n                        store: formatDisplay(store.$id),\n                        info: formatDisplay(`HMR update`),\n                    },\n                },\n            });\n            // update the devtools too\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(INSPECTOR_ID);\n            api.sendInspectorState(INSPECTOR_ID);\n        });\n        const { $dispose } = store;\n        store.$dispose = () => {\n            $dispose();\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(INSPECTOR_ID);\n            api.sendInspectorState(INSPECTOR_ID);\n            api.getSettings().logStoreChanges &&\n                toastMessage(`Disposed \"${store.$id}\" store 🗑`);\n        };\n        // trigger an update so it can display new registered stores\n        api.notifyComponentUpdate();\n        api.sendInspectorTree(INSPECTOR_ID);\n        api.sendInspectorState(INSPECTOR_ID);\n        api.getSettings().logStoreChanges &&\n            toastMessage(`\"${store.$id}\" store installed 🆕`);\n    });\n}\nlet runningActionId = 0;\nlet activeAction;\n/**\n * Patches a store to enable action grouping in devtools by wrapping the store with a Proxy that is passed as the\n * context of all actions, allowing us to set `runningAction` on each access and effectively associating any state\n * mutation to the action.\n *\n * @param store - store to patch\n * @param actionNames - list of actionst to patch\n */\nfunction patchActionForGrouping(store, actionNames, wrapWithProxy) {\n    // original actions of the store as they are given by pinia. We are going to override them\n    const actions = actionNames.reduce((storeActions, actionName) => {\n        // use toRaw to avoid tracking #541\n        storeActions[actionName] = toRaw(store)[actionName];\n        return storeActions;\n    }, {});\n    for (const actionName in actions) {\n        store[actionName] = function () {\n            // the running action id is incremented in a before action hook\n            const _actionId = runningActionId;\n            const trackedStore = wrapWithProxy\n                ? new Proxy(store, {\n                    get(...args) {\n                        activeAction = _actionId;\n                        return Reflect.get(...args);\n                    },\n                    set(...args) {\n                        activeAction = _actionId;\n                        return Reflect.set(...args);\n                    },\n                })\n                : store;\n            // For Setup Stores we need https://github.com/tc39/proposal-async-context\n            activeAction = _actionId;\n            const retValue = actions[actionName].apply(trackedStore, arguments);\n            // this is safer as async actions in Setup Stores would associate mutations done outside of the action\n            activeAction = undefined;\n            return retValue;\n        };\n    }\n}\n/**\n * pinia.use(devtoolsPlugin)\n */\nfunction devtoolsPlugin({ app, store, options }) {\n    // HMR module\n    if (store.$id.startsWith('__hot:')) {\n        return;\n    }\n    // detect option api vs setup api\n    store._isOptionsAPI = !!options.state;\n    // Do not overwrite actions mocked by @pinia/testing (#2298)\n    if (!store._p._testing) {\n        patchActionForGrouping(store, Object.keys(options.actions), store._isOptionsAPI);\n        // Upgrade the HMR to also update the new actions\n        const originalHotUpdate = store._hotUpdate;\n        toRaw(store)._hotUpdate = function (newStore) {\n            originalHotUpdate.apply(this, arguments);\n            patchActionForGrouping(store, Object.keys(newStore._hmrPayload.actions), !!store._isOptionsAPI);\n        };\n    }\n    addStoreToDevtools(app, \n    // FIXME: is there a way to allow the assignment from Store<Id, S, G, A> to StoreGeneric?\n    store);\n}\n\n/**\n * Creates a Pinia instance to be used by the application\n */\nfunction createPinia() {\n    const scope = effectScope(true);\n    // NOTE: here we could check the window object for a state and directly set it\n    // if there is anything like it with Vue 3 SSR\n    const state = scope.run(() => ref({}));\n    let _p = [];\n    // plugins added before calling app.use(pinia)\n    let toBeInstalled = [];\n    const pinia = markRaw({\n        install(app) {\n            // this allows calling useStore() outside of a component setup after\n            // installing pinia's plugin\n            setActivePinia(pinia);\n            if (!isVue2) {\n                pinia._a = app;\n                app.provide(piniaSymbol, pinia);\n                app.config.globalProperties.$pinia = pinia;\n                /* istanbul ignore else */\n                if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n                    registerPiniaDevtools(app, pinia);\n                }\n                toBeInstalled.forEach((plugin) => _p.push(plugin));\n                toBeInstalled = [];\n            }\n        },\n        use(plugin) {\n            if (!this._a && !isVue2) {\n                toBeInstalled.push(plugin);\n            }\n            else {\n                _p.push(plugin);\n            }\n            return this;\n        },\n        _p,\n        // it's actually undefined here\n        // @ts-expect-error\n        _a: null,\n        _e: scope,\n        _s: new Map(),\n        state,\n    });\n    // pinia devtools rely on dev only features so they cannot be forced unless\n    // the dev build of Vue is used. Avoid old browsers like IE11.\n    if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT && typeof Proxy !== 'undefined') {\n        pinia.use(devtoolsPlugin);\n    }\n    return pinia;\n}\n/**\n * Dispose a Pinia instance by stopping its effectScope and removing the state, plugins and stores. This is mostly\n * useful in tests, with both a testing pinia or a regular pinia and in applications that use multiple pinia instances.\n * Once disposed, the pinia instance cannot be used anymore.\n *\n * @param pinia - pinia instance\n */\nfunction disposePinia(pinia) {\n    pinia._e.stop();\n    pinia._s.clear();\n    pinia._p.splice(0);\n    pinia.state.value = {};\n    // @ts-expect-error: non valid\n    pinia._a = null;\n}\n\n/**\n * Checks if a function is a `StoreDefinition`.\n *\n * @param fn - object to test\n * @returns true if `fn` is a StoreDefinition\n */\nconst isUseStore = (fn) => {\n    return typeof fn === 'function' && typeof fn.$id === 'string';\n};\n/**\n * Mutates in place `newState` with `oldState` to _hot update_ it. It will\n * remove any key not existing in `newState` and recursively merge plain\n * objects.\n *\n * @param newState - new state object to be patched\n * @param oldState - old state that should be used to patch newState\n * @returns - newState\n */\nfunction patchObject(newState, oldState) {\n    // no need to go through symbols because they cannot be serialized anyway\n    for (const key in oldState) {\n        const subPatch = oldState[key];\n        // skip the whole sub tree\n        if (!(key in newState)) {\n            continue;\n        }\n        const targetValue = newState[key];\n        if (isPlainObject(targetValue) &&\n            isPlainObject(subPatch) &&\n            !isRef(subPatch) &&\n            !isReactive(subPatch)) {\n            newState[key] = patchObject(targetValue, subPatch);\n        }\n        else {\n            // objects are either a bit more complex (e.g. refs) or primitives, so we\n            // just set the whole thing\n            if (isVue2) {\n                set(newState, key, subPatch);\n            }\n            else {\n                newState[key] = subPatch;\n            }\n        }\n    }\n    return newState;\n}\n/**\n * Creates an _accept_ function to pass to `import.meta.hot` in Vite applications.\n *\n * @example\n * ```js\n * const useUser = defineStore(...)\n * if (import.meta.hot) {\n *   import.meta.hot.accept(acceptHMRUpdate(useUser, import.meta.hot))\n * }\n * ```\n *\n * @param initialUseStore - return of the defineStore to hot update\n * @param hot - `import.meta.hot`\n */\nfunction acceptHMRUpdate(initialUseStore, hot) {\n    // strip as much as possible from iife.prod\n    if (!(process.env.NODE_ENV !== 'production')) {\n        return () => { };\n    }\n    return (newModule) => {\n        const pinia = hot.data.pinia || initialUseStore._pinia;\n        if (!pinia) {\n            // this store is still not used\n            return;\n        }\n        // preserve the pinia instance across loads\n        hot.data.pinia = pinia;\n        // console.log('got data', newStore)\n        for (const exportName in newModule) {\n            const useStore = newModule[exportName];\n            // console.log('checking for', exportName)\n            if (isUseStore(useStore) && pinia._s.has(useStore.$id)) {\n                // console.log('Accepting update for', useStore.$id)\n                const id = useStore.$id;\n                if (id !== initialUseStore.$id) {\n                    console.warn(`The id of the store changed from \"${initialUseStore.$id}\" to \"${id}\". Reloading.`);\n                    // return import.meta.hot.invalidate()\n                    return hot.invalidate();\n                }\n                const existingStore = pinia._s.get(id);\n                if (!existingStore) {\n                    console.log(`[Pinia]: skipping hmr because store doesn't exist yet`);\n                    return;\n                }\n                useStore(pinia, existingStore);\n            }\n        }\n    };\n}\n\nconst noop = () => { };\nfunction addSubscription(subscriptions, callback, detached, onCleanup = noop) {\n    subscriptions.push(callback);\n    const removeSubscription = () => {\n        const idx = subscriptions.indexOf(callback);\n        if (idx > -1) {\n            subscriptions.splice(idx, 1);\n            onCleanup();\n        }\n    };\n    if (!detached && getCurrentScope()) {\n        onScopeDispose(removeSubscription);\n    }\n    return removeSubscription;\n}\nfunction triggerSubscriptions(subscriptions, ...args) {\n    subscriptions.slice().forEach((callback) => {\n        callback(...args);\n    });\n}\n\nconst fallbackRunWithContext = (fn) => fn();\n/**\n * Marks a function as an action for `$onAction`\n * @internal\n */\nconst ACTION_MARKER = Symbol();\n/**\n * Action name symbol. Allows to add a name to an action after defining it\n * @internal\n */\nconst ACTION_NAME = Symbol();\nfunction mergeReactiveObjects(target, patchToApply) {\n    // Handle Map instances\n    if (target instanceof Map && patchToApply instanceof Map) {\n        patchToApply.forEach((value, key) => target.set(key, value));\n    }\n    else if (target instanceof Set && patchToApply instanceof Set) {\n        // Handle Set instances\n        patchToApply.forEach(target.add, target);\n    }\n    // no need to go through symbols because they cannot be serialized anyway\n    for (const key in patchToApply) {\n        if (!patchToApply.hasOwnProperty(key))\n            continue;\n        const subPatch = patchToApply[key];\n        const targetValue = target[key];\n        if (isPlainObject(targetValue) &&\n            isPlainObject(subPatch) &&\n            target.hasOwnProperty(key) &&\n            !isRef(subPatch) &&\n            !isReactive(subPatch)) {\n            // NOTE: here I wanted to warn about inconsistent types but it's not possible because in setup stores one might\n            // start the value of a property as a certain type e.g. a Map, and then for some reason, during SSR, change that\n            // to `undefined`. When trying to hydrate, we want to override the Map with `undefined`.\n            target[key] = mergeReactiveObjects(targetValue, subPatch);\n        }\n        else {\n            // @ts-expect-error: subPatch is a valid value\n            target[key] = subPatch;\n        }\n    }\n    return target;\n}\nconst skipHydrateSymbol = (process.env.NODE_ENV !== 'production')\n    ? Symbol('pinia:skipHydration')\n    : /* istanbul ignore next */ Symbol();\n/**\n * Tells Pinia to skip the hydration process of a given object. This is useful in setup stores (only) when you return a\n * stateful object in the store but it isn't really state. e.g. returning a router instance in a setup store.\n *\n * @param obj - target object\n * @returns obj\n */\nfunction skipHydrate(obj) {\n    return Object.defineProperty(obj, skipHydrateSymbol, {});\n}\n/**\n * Returns whether a value should be hydrated\n *\n * @param obj - target variable\n * @returns true if `obj` should be hydrated\n */\nfunction shouldHydrate(obj) {\n    return !isPlainObject(obj) || !obj.hasOwnProperty(skipHydrateSymbol);\n}\nconst { assign } = Object;\nfunction isComputed(o) {\n    return !!(isRef(o) && o.effect);\n}\nfunction createOptionsStore(id, options, pinia, hot) {\n    const { state, actions, getters } = options;\n    const initialState = pinia.state.value[id];\n    let store;\n    function setup() {\n        if (!initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n            /* istanbul ignore if */\n            if (isVue2) {\n                set(pinia.state.value, id, state ? state() : {});\n            }\n            else {\n                pinia.state.value[id] = state ? state() : {};\n            }\n        }\n        // avoid creating a state in pinia.state.value\n        const localState = (process.env.NODE_ENV !== 'production') && hot\n            ? // use ref() to unwrap refs inside state TODO: check if this is still necessary\n                toRefs(ref(state ? state() : {}).value)\n            : toRefs(pinia.state.value[id]);\n        return assign(localState, actions, Object.keys(getters || {}).reduce((computedGetters, name) => {\n            if ((process.env.NODE_ENV !== 'production') && name in localState) {\n                console.warn(`[🍍]: A getter cannot have the same name as another state property. Rename one of them. Found with \"${name}\" in store \"${id}\".`);\n            }\n            computedGetters[name] = markRaw(computed(() => {\n                setActivePinia(pinia);\n                // it was created just before\n                const store = pinia._s.get(id);\n                // allow cross using stores\n                /* istanbul ignore if */\n                if (isVue2 && !store._r)\n                    return;\n                // @ts-expect-error\n                // return getters![name].call(context, context)\n                // TODO: avoid reading the getter while assigning with a global variable\n                return getters[name].call(store, store);\n            }));\n            return computedGetters;\n        }, {}));\n    }\n    store = createSetupStore(id, setup, options, pinia, hot, true);\n    return store;\n}\nfunction createSetupStore($id, setup, options = {}, pinia, hot, isOptionsStore) {\n    let scope;\n    const optionsForPlugin = assign({ actions: {} }, options);\n    /* istanbul ignore if */\n    if ((process.env.NODE_ENV !== 'production') && !pinia._e.active) {\n        throw new Error('Pinia destroyed');\n    }\n    // watcher options for $subscribe\n    const $subscribeOptions = { deep: true };\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production') && !isVue2) {\n        $subscribeOptions.onTrigger = (event) => {\n            /* istanbul ignore else */\n            if (isListening) {\n                debuggerEvents = event;\n                // avoid triggering this while the store is being built and the state is being set in pinia\n            }\n            else if (isListening == false && !store._hotUpdating) {\n                // let patch send all the events together later\n                /* istanbul ignore else */\n                if (Array.isArray(debuggerEvents)) {\n                    debuggerEvents.push(event);\n                }\n                else {\n                    console.error('🍍 debuggerEvents should be an array. This is most likely an internal Pinia bug.');\n                }\n            }\n        };\n    }\n    // internal state\n    let isListening; // set to true at the end\n    let isSyncListening; // set to true at the end\n    let subscriptions = [];\n    let actionSubscriptions = [];\n    let debuggerEvents;\n    const initialState = pinia.state.value[$id];\n    // avoid setting the state for option stores if it is set\n    // by the setup\n    if (!isOptionsStore && !initialState && (!(process.env.NODE_ENV !== 'production') || !hot)) {\n        /* istanbul ignore if */\n        if (isVue2) {\n            set(pinia.state.value, $id, {});\n        }\n        else {\n            pinia.state.value[$id] = {};\n        }\n    }\n    const hotState = ref({});\n    // avoid triggering too many listeners\n    // https://github.com/vuejs/pinia/issues/1129\n    let activeListener;\n    function $patch(partialStateOrMutator) {\n        let subscriptionMutation;\n        isListening = isSyncListening = false;\n        // reset the debugger events since patches are sync\n        /* istanbul ignore else */\n        if ((process.env.NODE_ENV !== 'production')) {\n            debuggerEvents = [];\n        }\n        if (typeof partialStateOrMutator === 'function') {\n            partialStateOrMutator(pinia.state.value[$id]);\n            subscriptionMutation = {\n                type: MutationType.patchFunction,\n                storeId: $id,\n                events: debuggerEvents,\n            };\n        }\n        else {\n            mergeReactiveObjects(pinia.state.value[$id], partialStateOrMutator);\n            subscriptionMutation = {\n                type: MutationType.patchObject,\n                payload: partialStateOrMutator,\n                storeId: $id,\n                events: debuggerEvents,\n            };\n        }\n        const myListenerId = (activeListener = Symbol());\n        nextTick().then(() => {\n            if (activeListener === myListenerId) {\n                isListening = true;\n            }\n        });\n        isSyncListening = true;\n        // because we paused the watcher, we need to manually call the subscriptions\n        triggerSubscriptions(subscriptions, subscriptionMutation, pinia.state.value[$id]);\n    }\n    const $reset = isOptionsStore\n        ? function $reset() {\n            const { state } = options;\n            const newState = state ? state() : {};\n            // we use a patch to group all changes into one single subscription\n            this.$patch(($state) => {\n                // @ts-expect-error: FIXME: shouldn't error?\n                assign($state, newState);\n            });\n        }\n        : /* istanbul ignore next */\n            (process.env.NODE_ENV !== 'production')\n                ? () => {\n                    throw new Error(`🍍: Store \"${$id}\" is built using the setup syntax and does not implement $reset().`);\n                }\n                : noop;\n    function $dispose() {\n        scope.stop();\n        subscriptions = [];\n        actionSubscriptions = [];\n        pinia._s.delete($id);\n    }\n    /**\n     * Helper that wraps function so it can be tracked with $onAction\n     * @param fn - action to wrap\n     * @param name - name of the action\n     */\n    const action = (fn, name = '') => {\n        if (ACTION_MARKER in fn) {\n            fn[ACTION_NAME] = name;\n            return fn;\n        }\n        const wrappedAction = function () {\n            setActivePinia(pinia);\n            const args = Array.from(arguments);\n            const afterCallbackList = [];\n            const onErrorCallbackList = [];\n            function after(callback) {\n                afterCallbackList.push(callback);\n            }\n            function onError(callback) {\n                onErrorCallbackList.push(callback);\n            }\n            // @ts-expect-error\n            triggerSubscriptions(actionSubscriptions, {\n                args,\n                name: wrappedAction[ACTION_NAME],\n                store,\n                after,\n                onError,\n            });\n            let ret;\n            try {\n                ret = fn.apply(this && this.$id === $id ? this : store, args);\n                // handle sync errors\n            }\n            catch (error) {\n                triggerSubscriptions(onErrorCallbackList, error);\n                throw error;\n            }\n            if (ret instanceof Promise) {\n                return ret\n                    .then((value) => {\n                    triggerSubscriptions(afterCallbackList, value);\n                    return value;\n                })\n                    .catch((error) => {\n                    triggerSubscriptions(onErrorCallbackList, error);\n                    return Promise.reject(error);\n                });\n            }\n            // trigger after callbacks\n            triggerSubscriptions(afterCallbackList, ret);\n            return ret;\n        };\n        wrappedAction[ACTION_MARKER] = true;\n        wrappedAction[ACTION_NAME] = name; // will be set later\n        // @ts-expect-error: we are intentionally limiting the returned type to just Fn\n        // because all the added properties are internals that are exposed through `$onAction()` only\n        return wrappedAction;\n    };\n    const _hmrPayload = /*#__PURE__*/ markRaw({\n        actions: {},\n        getters: {},\n        state: [],\n        hotState,\n    });\n    const partialStore = {\n        _p: pinia,\n        // _s: scope,\n        $id,\n        $onAction: addSubscription.bind(null, actionSubscriptions),\n        $patch,\n        $reset,\n        $subscribe(callback, options = {}) {\n            const removeSubscription = addSubscription(subscriptions, callback, options.detached, () => stopWatcher());\n            const stopWatcher = scope.run(() => watch(() => pinia.state.value[$id], (state) => {\n                if (options.flush === 'sync' ? isSyncListening : isListening) {\n                    callback({\n                        storeId: $id,\n                        type: MutationType.direct,\n                        events: debuggerEvents,\n                    }, state);\n                }\n            }, assign({}, $subscribeOptions, options)));\n            return removeSubscription;\n        },\n        $dispose,\n    };\n    /* istanbul ignore if */\n    if (isVue2) {\n        // start as non ready\n        partialStore._r = false;\n    }\n    const store = reactive((process.env.NODE_ENV !== 'production') || ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT)\n        ? assign({\n            _hmrPayload,\n            _customProperties: markRaw(new Set()), // devtools custom properties\n        }, partialStore\n        // must be added later\n        // setupStore\n        )\n        : partialStore);\n    // store the partial store now so the setup of stores can instantiate each other before they are finished without\n    // creating infinite loops.\n    pinia._s.set($id, store);\n    const runWithContext = (pinia._a && pinia._a.runWithContext) || fallbackRunWithContext;\n    // TODO: idea create skipSerialize that marks properties as non serializable and they are skipped\n    const setupStore = runWithContext(() => pinia._e.run(() => (scope = effectScope()).run(() => setup({ action }))));\n    // overwrite existing actions to support $onAction\n    for (const key in setupStore) {\n        const prop = setupStore[key];\n        if ((isRef(prop) && !isComputed(prop)) || isReactive(prop)) {\n            // mark it as a piece of state to be serialized\n            if ((process.env.NODE_ENV !== 'production') && hot) {\n                set(hotState.value, key, toRef(setupStore, key));\n                // createOptionStore directly sets the state in pinia.state.value so we\n                // can just skip that\n            }\n            else if (!isOptionsStore) {\n                // in setup stores we must hydrate the state and sync pinia state tree with the refs the user just created\n                if (initialState && shouldHydrate(prop)) {\n                    if (isRef(prop)) {\n                        prop.value = initialState[key];\n                    }\n                    else {\n                        // probably a reactive object, lets recursively assign\n                        // @ts-expect-error: prop is unknown\n                        mergeReactiveObjects(prop, initialState[key]);\n                    }\n                }\n                // transfer the ref to the pinia state to keep everything in sync\n                /* istanbul ignore if */\n                if (isVue2) {\n                    set(pinia.state.value[$id], key, prop);\n                }\n                else {\n                    pinia.state.value[$id][key] = prop;\n                }\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                _hmrPayload.state.push(key);\n            }\n            // action\n        }\n        else if (typeof prop === 'function') {\n            const actionValue = (process.env.NODE_ENV !== 'production') && hot ? prop : action(prop, key);\n            // this a hot module replacement store because the hotUpdate method needs\n            // to do it with the right context\n            /* istanbul ignore if */\n            if (isVue2) {\n                set(setupStore, key, actionValue);\n            }\n            else {\n                // @ts-expect-error\n                setupStore[key] = actionValue;\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                _hmrPayload.actions[key] = prop;\n            }\n            // list actions so they can be used in plugins\n            // @ts-expect-error\n            optionsForPlugin.actions[key] = prop;\n        }\n        else if ((process.env.NODE_ENV !== 'production')) {\n            // add getters for devtools\n            if (isComputed(prop)) {\n                _hmrPayload.getters[key] = isOptionsStore\n                    ? // @ts-expect-error\n                        options.getters[key]\n                    : prop;\n                if (IS_CLIENT) {\n                    const getters = setupStore._getters ||\n                        // @ts-expect-error: same\n                        (setupStore._getters = markRaw([]));\n                    getters.push(key);\n                }\n            }\n        }\n    }\n    // add the state, getters, and action properties\n    /* istanbul ignore if */\n    if (isVue2) {\n        Object.keys(setupStore).forEach((key) => {\n            set(store, key, setupStore[key]);\n        });\n    }\n    else {\n        assign(store, setupStore);\n        // allows retrieving reactive objects with `storeToRefs()`. Must be called after assigning to the reactive object.\n        // Make `storeToRefs()` work with `reactive()` #799\n        assign(toRaw(store), setupStore);\n    }\n    // use this instead of a computed with setter to be able to create it anywhere\n    // without linking the computed lifespan to wherever the store is first\n    // created.\n    Object.defineProperty(store, '$state', {\n        get: () => ((process.env.NODE_ENV !== 'production') && hot ? hotState.value : pinia.state.value[$id]),\n        set: (state) => {\n            /* istanbul ignore if */\n            if ((process.env.NODE_ENV !== 'production') && hot) {\n                throw new Error('cannot set hotState');\n            }\n            $patch(($state) => {\n                // @ts-expect-error: FIXME: shouldn't error?\n                assign($state, state);\n            });\n        },\n    });\n    // add the hotUpdate before plugins to allow them to override it\n    /* istanbul ignore else */\n    if ((process.env.NODE_ENV !== 'production')) {\n        store._hotUpdate = markRaw((newStore) => {\n            store._hotUpdating = true;\n            newStore._hmrPayload.state.forEach((stateKey) => {\n                if (stateKey in store.$state) {\n                    const newStateTarget = newStore.$state[stateKey];\n                    const oldStateSource = store.$state[stateKey];\n                    if (typeof newStateTarget === 'object' &&\n                        isPlainObject(newStateTarget) &&\n                        isPlainObject(oldStateSource)) {\n                        patchObject(newStateTarget, oldStateSource);\n                    }\n                    else {\n                        // transfer the ref\n                        newStore.$state[stateKey] = oldStateSource;\n                    }\n                }\n                // patch direct access properties to allow store.stateProperty to work as\n                // store.$state.stateProperty\n                set(store, stateKey, toRef(newStore.$state, stateKey));\n            });\n            // remove deleted state properties\n            Object.keys(store.$state).forEach((stateKey) => {\n                if (!(stateKey in newStore.$state)) {\n                    del(store, stateKey);\n                }\n            });\n            // avoid devtools logging this as a mutation\n            isListening = false;\n            isSyncListening = false;\n            pinia.state.value[$id] = toRef(newStore._hmrPayload, 'hotState');\n            isSyncListening = true;\n            nextTick().then(() => {\n                isListening = true;\n            });\n            for (const actionName in newStore._hmrPayload.actions) {\n                const actionFn = newStore[actionName];\n                set(store, actionName, action(actionFn, actionName));\n            }\n            // TODO: does this work in both setup and option store?\n            for (const getterName in newStore._hmrPayload.getters) {\n                const getter = newStore._hmrPayload.getters[getterName];\n                const getterValue = isOptionsStore\n                    ? // special handling of options api\n                        computed(() => {\n                            setActivePinia(pinia);\n                            return getter.call(store, store);\n                        })\n                    : getter;\n                set(store, getterName, getterValue);\n            }\n            // remove deleted getters\n            Object.keys(store._hmrPayload.getters).forEach((key) => {\n                if (!(key in newStore._hmrPayload.getters)) {\n                    del(store, key);\n                }\n            });\n            // remove old actions\n            Object.keys(store._hmrPayload.actions).forEach((key) => {\n                if (!(key in newStore._hmrPayload.actions)) {\n                    del(store, key);\n                }\n            });\n            // update the values used in devtools and to allow deleting new properties later on\n            store._hmrPayload = newStore._hmrPayload;\n            store._getters = newStore._getters;\n            store._hotUpdating = false;\n        });\n    }\n    if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n        const nonEnumerable = {\n            writable: true,\n            configurable: true,\n            // avoid warning on devtools trying to display this property\n            enumerable: false,\n        };\n        ['_p', '_hmrPayload', '_getters', '_customProperties'].forEach((p) => {\n            Object.defineProperty(store, p, assign({ value: store[p] }, nonEnumerable));\n        });\n    }\n    /* istanbul ignore if */\n    if (isVue2) {\n        // mark the store as ready before plugins\n        store._r = true;\n    }\n    // apply all plugins\n    pinia._p.forEach((extender) => {\n        /* istanbul ignore else */\n        if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n            const extensions = scope.run(() => extender({\n                store: store,\n                app: pinia._a,\n                pinia,\n                options: optionsForPlugin,\n            }));\n            Object.keys(extensions || {}).forEach((key) => store._customProperties.add(key));\n            assign(store, extensions);\n        }\n        else {\n            assign(store, scope.run(() => extender({\n                store: store,\n                app: pinia._a,\n                pinia,\n                options: optionsForPlugin,\n            })));\n        }\n    });\n    if ((process.env.NODE_ENV !== 'production') &&\n        store.$state &&\n        typeof store.$state === 'object' &&\n        typeof store.$state.constructor === 'function' &&\n        !store.$state.constructor.toString().includes('[native code]')) {\n        console.warn(`[🍍]: The \"state\" must be a plain object. It cannot be\\n` +\n            `\\tstate: () => new MyClass()\\n` +\n            `Found in store \"${store.$id}\".`);\n    }\n    // only apply hydrate to option stores with an initial state in pinia\n    if (initialState &&\n        isOptionsStore &&\n        options.hydrate) {\n        options.hydrate(store.$state, initialState);\n    }\n    isListening = true;\n    isSyncListening = true;\n    return store;\n}\n// allows unused stores to be tree shaken\n/*! #__NO_SIDE_EFFECTS__ */\nfunction defineStore(\n// TODO: add proper types from above\nidOrOptions, setup, setupOptions) {\n    let id;\n    let options;\n    const isSetupStore = typeof setup === 'function';\n    if (typeof idOrOptions === 'string') {\n        id = idOrOptions;\n        // the option store setup will contain the actual options in this case\n        options = isSetupStore ? setupOptions : setup;\n    }\n    else {\n        options = idOrOptions;\n        id = idOrOptions.id;\n        if ((process.env.NODE_ENV !== 'production') && typeof id !== 'string') {\n            throw new Error(`[🍍]: \"defineStore()\" must be passed a store id as its first argument.`);\n        }\n    }\n    function useStore(pinia, hot) {\n        const hasContext = hasInjectionContext();\n        pinia =\n            // in test mode, ignore the argument provided as we can always retrieve a\n            // pinia instance with getActivePinia()\n            ((process.env.NODE_ENV === 'test') && activePinia && activePinia._testing ? null : pinia) ||\n                (hasContext ? inject(piniaSymbol, null) : null);\n        if (pinia)\n            setActivePinia(pinia);\n        if ((process.env.NODE_ENV !== 'production') && !activePinia) {\n            throw new Error(`[🍍]: \"getActivePinia()\" was called but there was no active Pinia. Are you trying to use a store before calling \"app.use(pinia)\"?\\n` +\n                `See https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\\n` +\n                `This will fail in production.`);\n        }\n        pinia = activePinia;\n        if (!pinia._s.has(id)) {\n            // creating the store registers it in `pinia._s`\n            if (isSetupStore) {\n                createSetupStore(id, setup, options, pinia);\n            }\n            else {\n                createOptionsStore(id, options, pinia);\n            }\n            /* istanbul ignore else */\n            if ((process.env.NODE_ENV !== 'production')) {\n                // @ts-expect-error: not the right inferred type\n                useStore._pinia = pinia;\n            }\n        }\n        const store = pinia._s.get(id);\n        if ((process.env.NODE_ENV !== 'production') && hot) {\n            const hotId = '__hot:' + id;\n            const newStore = isSetupStore\n                ? createSetupStore(hotId, setup, options, pinia, true)\n                : createOptionsStore(hotId, assign({}, options), pinia, true);\n            hot._hotUpdate(newStore);\n            // cleanup the state properties and the store from the cache\n            delete pinia.state.value[hotId];\n            pinia._s.delete(hotId);\n        }\n        if ((process.env.NODE_ENV !== 'production') && IS_CLIENT) {\n            const currentInstance = getCurrentInstance();\n            // save stores in instances to access them devtools\n            if (currentInstance &&\n                currentInstance.proxy &&\n                // avoid adding stores that are just built for hot module replacement\n                !hot) {\n                const vm = currentInstance.proxy;\n                const cache = '_pStores' in vm ? vm._pStores : (vm._pStores = {});\n                cache[id] = store;\n            }\n        }\n        // StoreGeneric cannot be casted towards Store\n        return store;\n    }\n    useStore.$id = id;\n    return useStore;\n}\n\nlet mapStoreSuffix = 'Store';\n/**\n * Changes the suffix added by `mapStores()`. Can be set to an empty string.\n * Defaults to `\"Store\"`. Make sure to extend the MapStoresCustomization\n * interface if you are using TypeScript.\n *\n * @param suffix - new suffix\n */\nfunction setMapStoreSuffix(suffix // could be 'Store' but that would be annoying for JS\n) {\n    mapStoreSuffix = suffix;\n}\n/**\n * Allows using stores without the composition API (`setup()`) by generating an\n * object to be spread in the `computed` field of a component. It accepts a list\n * of store definitions.\n *\n * @example\n * ```js\n * export default {\n *   computed: {\n *     // other computed properties\n *     ...mapStores(useUserStore, useCartStore)\n *   },\n *\n *   created() {\n *     this.userStore // store with id \"user\"\n *     this.cartStore // store with id \"cart\"\n *   }\n * }\n * ```\n *\n * @param stores - list of stores to map to an object\n */\nfunction mapStores(...stores) {\n    if ((process.env.NODE_ENV !== 'production') && Array.isArray(stores[0])) {\n        console.warn(`[🍍]: Directly pass all stores to \"mapStores()\" without putting them in an array:\\n` +\n            `Replace\\n` +\n            `\\tmapStores([useAuthStore, useCartStore])\\n` +\n            `with\\n` +\n            `\\tmapStores(useAuthStore, useCartStore)\\n` +\n            `This will fail in production if not fixed.`);\n        stores = stores[0];\n    }\n    return stores.reduce((reduced, useStore) => {\n        // @ts-expect-error: $id is added by defineStore\n        reduced[useStore.$id + mapStoreSuffix] = function () {\n            return useStore(this.$pinia);\n        };\n        return reduced;\n    }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapState(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            reduced[key] = function () {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[key];\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function () {\n                const store = useStore(this.$pinia);\n                const storeKey = keysOrMapper[key];\n                // for some reason TS is unable to infer the type of storeKey to be a\n                // function\n                return typeof storeKey === 'function'\n                    ? storeKey.call(this, store)\n                    : // @ts-expect-error: FIXME: should work?\n                        store[storeKey];\n            };\n            return reduced;\n        }, {});\n}\n/**\n * Alias for `mapState()`. You should use `mapState()` instead.\n * @deprecated use `mapState()` instead.\n */\nconst mapGetters = mapState;\n/**\n * Allows directly using actions from your store without using the composition\n * API (`setup()`) by generating an object to be spread in the `methods` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapActions(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function (...args) {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[key](...args);\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            // @ts-expect-error\n            reduced[key] = function (...args) {\n                // @ts-expect-error: FIXME: should work?\n                return useStore(this.$pinia)[keysOrMapper[key]](...args);\n            };\n            return reduced;\n        }, {});\n}\n/**\n * Allows using state and getters from one store without using the composition\n * API (`setup()`) by generating an object to be spread in the `computed` field\n * of a component.\n *\n * @param useStore - store to map from\n * @param keysOrMapper - array or object\n */\nfunction mapWritableState(useStore, keysOrMapper) {\n    return Array.isArray(keysOrMapper)\n        ? keysOrMapper.reduce((reduced, key) => {\n            reduced[key] = {\n                get() {\n                    return useStore(this.$pinia)[key];\n                },\n                set(value) {\n                    return (useStore(this.$pinia)[key] = value);\n                },\n            };\n            return reduced;\n        }, {})\n        : Object.keys(keysOrMapper).reduce((reduced, key) => {\n            reduced[key] = {\n                get() {\n                    return useStore(this.$pinia)[keysOrMapper[key]];\n                },\n                set(value) {\n                    return (useStore(this.$pinia)[keysOrMapper[key]] = value);\n                },\n            };\n            return reduced;\n        }, {});\n}\n\n/**\n * Creates an object of references with all the state, getters, and plugin-added\n * state properties of the store. Similar to `toRefs()` but specifically\n * designed for Pinia stores so methods and non reactive properties are\n * completely ignored.\n *\n * @param store - store to extract the refs from\n */\nfunction storeToRefs(store) {\n    // See https://github.com/vuejs/pinia/issues/852\n    // It's easier to just use toRefs() even if it includes more stuff\n    if (isVue2) {\n        // @ts-expect-error: toRefs include methods and others\n        return toRefs(store);\n    }\n    else {\n        const rawStore = toRaw(store);\n        const refs = {};\n        for (const key in rawStore) {\n            const value = rawStore[key];\n            // There is no native method to check for a computed\n            // https://github.com/vuejs/core/pull/4165\n            if (value.effect) {\n                // @ts-expect-error: too hard to type correctly\n                refs[key] =\n                    // ...\n                    computed({\n                        get: () => store[key],\n                        set(value) {\n                            store[key] = value;\n                        },\n                    });\n            }\n            else if (isRef(value) || isReactive(value)) {\n                // @ts-expect-error: the key is state or getter\n                refs[key] =\n                    // ---\n                    toRef(store, key);\n            }\n        }\n        return refs;\n    }\n}\n\n/**\n * Vue 2 Plugin that must be installed for pinia to work. Note **you don't need\n * this plugin if you are using Nuxt.js**. Use the `buildModule` instead:\n * https://pinia.vuejs.org/ssr/nuxt.html.\n *\n * @example\n * ```js\n * import Vue from 'vue'\n * import { PiniaVuePlugin, createPinia } from 'pinia'\n *\n * Vue.use(PiniaVuePlugin)\n * const pinia = createPinia()\n *\n * new Vue({\n *   el: '#app',\n *   // ...\n *   pinia,\n * })\n * ```\n *\n * @param _Vue - `Vue` imported from 'vue'.\n */\nconst PiniaVuePlugin = function (_Vue) {\n    // Equivalent of\n    // app.config.globalProperties.$pinia = pinia\n    _Vue.mixin({\n        beforeCreate() {\n            const options = this.$options;\n            if (options.pinia) {\n                const pinia = options.pinia;\n                // HACK: taken from provide(): https://github.com/vuejs/composition-api/blob/main/src/apis/inject.ts#L31\n                /* istanbul ignore else */\n                if (!this._provided) {\n                    const provideCache = {};\n                    Object.defineProperty(this, '_provided', {\n                        get: () => provideCache,\n                        set: (v) => Object.assign(provideCache, v),\n                    });\n                }\n                this._provided[piniaSymbol] = pinia;\n                // propagate the pinia instance in an SSR friendly way\n                // avoid adding it to nuxt twice\n                /* istanbul ignore else */\n                if (!this.$pinia) {\n                    this.$pinia = pinia;\n                }\n                pinia._a = this;\n                if (IS_CLIENT) {\n                    // this allows calling useStore() outside of a component setup after\n                    // installing pinia's plugin\n                    setActivePinia(pinia);\n                }\n                if ((((process.env.NODE_ENV !== 'production') || (typeof __VUE_PROD_DEVTOOLS__ !== 'undefined' && __VUE_PROD_DEVTOOLS__)) && !(process.env.NODE_ENV === 'test')) && IS_CLIENT) {\n                    registerPiniaDevtools(pinia._a, pinia);\n                }\n            }\n            else if (!this.$pinia && options.parent && options.parent.$pinia) {\n                this.$pinia = options.parent.$pinia;\n            }\n        },\n        destroyed() {\n            delete this._pStores;\n        },\n    });\n};\n\nexport { MutationType, PiniaVuePlugin, acceptHMRUpdate, createPinia, defineStore, disposePinia, getActivePinia, mapActions, mapGetters, mapState, mapStores, mapWritableState, setActivePinia, setMapStoreSuffix, shouldHydrate, skipHydrate, storeToRefs };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAI;AAQJ,IAAM,iBAAiB,CAAC,UAAW,cAAc;AAIjD,IAAM,iBAAiB,MAAO,oBAAoB,KAAK,OAAO,WAAW,KAAM;AAC/E,IAAM,cAAgB,OAAyC,OAAO,OAAO;AAAA;AAAA,EAA+B,OAAO;AAAA;AAEnH,SAAS,cAET,GAAG;AACC,SAAQ,KACJ,OAAO,MAAM,YACb,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM,qBACtC,OAAO,EAAE,WAAW;AAC5B;AAMA,IAAI;AAAA,CACH,SAAUA,eAAc;AAQrB,EAAAA,cAAa,QAAQ,IAAI;AAMzB,EAAAA,cAAa,aAAa,IAAI;AAM9B,EAAAA,cAAa,eAAe,IAAI;AAEpC,GAAG,iBAAiB,eAAe,CAAC,EAAE;AAEtC,IAAM,YAAY,OAAO,WAAW;AAYpC,IAAM,WAAyB,MAAM,OAAO,WAAW,YAAY,OAAO,WAAW,SAC/E,SACA,OAAO,SAAS,YAAY,KAAK,SAAS,OACtC,OACA,OAAO,WAAW,YAAY,OAAO,WAAW,SAC5C,SACA,OAAO,eAAe,WAClB,aACA,EAAE,aAAa,KAAK,GAAG;AACzC,SAAS,IAAI,MAAM,EAAE,UAAU,MAAM,IAAI,CAAC,GAAG;AAGzC,MAAI,WACA,6EAA6E,KAAK,KAAK,IAAI,GAAG;AAC9F,WAAO,IAAI,KAAK,CAAC,OAAO,aAAa,KAAM,GAAG,IAAI,GAAG,EAAE,MAAM,KAAK,KAAK,CAAC;AAAA,EAC5E;AACA,SAAO;AACX;AACA,SAAS,SAAS,KAAK,MAAM,MAAM;AAC/B,QAAM,MAAM,IAAI,eAAe;AAC/B,MAAI,KAAK,OAAO,GAAG;AACnB,MAAI,eAAe;AACnB,MAAI,SAAS,WAAY;AACrB,WAAO,IAAI,UAAU,MAAM,IAAI;AAAA,EACnC;AACA,MAAI,UAAU,WAAY;AACtB,YAAQ,MAAM,yBAAyB;AAAA,EAC3C;AACA,MAAI,KAAK;AACb;AACA,SAAS,YAAY,KAAK;AACtB,QAAM,MAAM,IAAI,eAAe;AAE/B,MAAI,KAAK,QAAQ,KAAK,KAAK;AAC3B,MAAI;AACA,QAAI,KAAK;AAAA,EACb,SACO,GAAG;AAAA,EAAE;AACZ,SAAO,IAAI,UAAU,OAAO,IAAI,UAAU;AAC9C;AAEA,SAAS,MAAM,MAAM;AACjB,MAAI;AACA,SAAK,cAAc,IAAI,WAAW,OAAO,CAAC;AAAA,EAC9C,SACO,GAAG;AACN,UAAM,MAAM,SAAS,YAAY,aAAa;AAC9C,QAAI,eAAe,SAAS,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,IAAI,IAAI,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AACpG,SAAK,cAAc,GAAG;AAAA,EAC1B;AACJ;AACA,IAAM,aAAa,OAAO,cAAc,WAAW,YAAY,EAAE,WAAW,GAAG;AAI/E,IAAM,kBAAgC,MAAM,YAAY,KAAK,WAAW,SAAS,KAC7E,cAAc,KAAK,WAAW,SAAS,KACvC,CAAC,SAAS,KAAK,WAAW,SAAS,GAAG;AAC1C,IAAM,SAAS,CAAC,YACV,MAAM;AAAE;AAAA;AAAA,EAEN,OAAO,sBAAsB,eACzB,cAAc,kBAAkB,aAChC,CAAC,iBACC;AAAA;AAAA,IAEE,sBAAsB,aAChB;AAAA;AAAA,MAEE;AAAA;AAAA;AAAA;AACxB,SAAS,eAAe,MAAM,OAAO,YAAY,MAAM;AACnD,QAAM,IAAI,SAAS,cAAc,GAAG;AACpC,IAAE,WAAW;AACb,IAAE,MAAM;AAGR,MAAI,OAAO,SAAS,UAAU;AAE1B,MAAE,OAAO;AACT,QAAI,EAAE,WAAW,SAAS,QAAQ;AAC9B,UAAI,YAAY,EAAE,IAAI,GAAG;AACrB,iBAAS,MAAM,MAAM,IAAI;AAAA,MAC7B,OACK;AACD,UAAE,SAAS;AACX,cAAM,CAAC;AAAA,MACX;AAAA,IACJ,OACK;AACD,YAAM,CAAC;AAAA,IACX;AAAA,EACJ,OACK;AAED,MAAE,OAAO,IAAI,gBAAgB,IAAI;AACjC,eAAW,WAAY;AACnB,UAAI,gBAAgB,EAAE,IAAI;AAAA,IAC9B,GAAG,GAAG;AACN,eAAW,WAAY;AACnB,YAAM,CAAC;AAAA,IACX,GAAG,CAAC;AAAA,EACR;AACJ;AACA,SAAS,SAAS,MAAM,OAAO,YAAY,MAAM;AAC7C,MAAI,OAAO,SAAS,UAAU;AAC1B,QAAI,YAAY,IAAI,GAAG;AACnB,eAAS,MAAM,MAAM,IAAI;AAAA,IAC7B,OACK;AACD,YAAM,IAAI,SAAS,cAAc,GAAG;AACpC,QAAE,OAAO;AACT,QAAE,SAAS;AACX,iBAAW,WAAY;AACnB,cAAM,CAAC;AAAA,MACX,CAAC;AAAA,IACL;AAAA,EACJ,OACK;AAED,cAAU,iBAAiB,IAAI,MAAM,IAAI,GAAG,IAAI;AAAA,EACpD;AACJ;AACA,SAAS,gBAAgB,MAAM,MAAM,MAAM,OAAO;AAG9C,UAAQ,SAAS,KAAK,IAAI,QAAQ;AAClC,MAAI,OAAO;AACP,UAAM,SAAS,QAAQ,MAAM,SAAS,KAAK,YAAY;AAAA,EAC3D;AACA,MAAI,OAAO,SAAS;AAChB,WAAO,SAAS,MAAM,MAAM,IAAI;AACpC,QAAM,QAAQ,KAAK,SAAS;AAC5B,QAAM,WAAW,eAAe,KAAK,OAAO,QAAQ,WAAW,CAAC,KAAK,YAAY;AACjF,QAAM,cAAc,eAAe,KAAK,UAAU,SAAS;AAC3D,OAAK,eAAgB,SAAS,YAAa,mBACvC,OAAO,eAAe,aAAa;AAEnC,UAAM,SAAS,IAAI,WAAW;AAC9B,WAAO,YAAY,WAAY;AAC3B,UAAI,MAAM,OAAO;AACjB,UAAI,OAAO,QAAQ,UAAU;AACzB,gBAAQ;AACR,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC9C;AACA,YAAM,cACA,MACA,IAAI,QAAQ,gBAAgB,uBAAuB;AACzD,UAAI,OAAO;AACP,cAAM,SAAS,OAAO;AAAA,MAC1B,OACK;AACD,iBAAS,OAAO,GAAG;AAAA,MACvB;AACA,cAAQ;AAAA,IACZ;AACA,WAAO,cAAc,IAAI;AAAA,EAC7B,OACK;AACD,UAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,QAAI;AACA,YAAM,SAAS,OAAO,GAAG;AAAA;AAEzB,eAAS,OAAO;AACpB,YAAQ;AACR,eAAW,WAAY;AACnB,UAAI,gBAAgB,GAAG;AAAA,IAC3B,GAAG,GAAG;AAAA,EACV;AACJ;AAQA,SAAS,aAAa,SAAS,MAAM;AACjC,QAAM,eAAe,QAAQ;AAC7B,MAAI,OAAO,2BAA2B,YAAY;AAE9C,2BAAuB,cAAc,IAAI;AAAA,EAC7C,WACS,SAAS,SAAS;AACvB,YAAQ,MAAM,YAAY;AAAA,EAC9B,WACS,SAAS,QAAQ;AACtB,YAAQ,KAAK,YAAY;AAAA,EAC7B,OACK;AACD,YAAQ,IAAI,YAAY;AAAA,EAC5B;AACJ;AACA,SAAS,QAAQ,GAAG;AAChB,SAAO,QAAQ,KAAK,aAAa;AACrC;AAMA,SAAS,uBAAuB;AAC5B,MAAI,EAAE,eAAe,YAAY;AAC7B,iBAAa,kDAAkD,OAAO;AACtE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,qBAAqB,OAAO;AACjC,MAAI,iBAAiB,SACjB,MAAM,QAAQ,YAAY,EAAE,SAAS,yBAAyB,GAAG;AACjE,iBAAa,mGAAmG,MAAM;AACtH,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,eAAe,sBAAsB,OAAO;AACxC,MAAI,qBAAqB;AACrB;AACJ,MAAI;AACA,UAAM,UAAU,UAAU,UAAU,KAAK,UAAU,MAAM,MAAM,KAAK,CAAC;AACrE,iBAAa,mCAAmC;AAAA,EACpD,SACO,OAAO;AACV,QAAI,qBAAqB,KAAK;AAC1B;AACJ,iBAAa,sEAAsE,OAAO;AAC1F,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,eAAe,uBAAuB,OAAO;AACzC,MAAI,qBAAqB;AACrB;AACJ,MAAI;AACA,oBAAgB,OAAO,KAAK,MAAM,MAAM,UAAU,UAAU,SAAS,CAAC,CAAC;AACvE,iBAAa,qCAAqC;AAAA,EACtD,SACO,OAAO;AACV,QAAI,qBAAqB,KAAK;AAC1B;AACJ,iBAAa,uFAAuF,OAAO;AAC3G,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,eAAe,sBAAsB,OAAO;AACxC,MAAI;AACA,WAAO,IAAI,KAAK,CAAC,KAAK,UAAU,MAAM,MAAM,KAAK,CAAC,GAAG;AAAA,MACjD,MAAM;AAAA,IACV,CAAC,GAAG,kBAAkB;AAAA,EAC1B,SACO,OAAO;AACV,iBAAa,2EAA2E,OAAO;AAC/F,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,IAAI;AACJ,SAAS,gBAAgB;AACrB,MAAI,CAAC,WAAW;AACZ,gBAAY,SAAS,cAAc,OAAO;AAC1C,cAAU,OAAO;AACjB,cAAU,SAAS;AAAA,EACvB;AACA,WAAS,WAAW;AAChB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,gBAAU,WAAW,YAAY;AAC7B,cAAM,QAAQ,UAAU;AACxB,YAAI,CAAC;AACD,iBAAO,QAAQ,IAAI;AACvB,cAAM,OAAO,MAAM,KAAK,CAAC;AACzB,YAAI,CAAC;AACD,iBAAO,QAAQ,IAAI;AACvB,eAAO,QAAQ,EAAE,MAAM,MAAM,KAAK,KAAK,GAAG,KAAK,CAAC;AAAA,MACpD;AAEA,gBAAU,WAAW,MAAM,QAAQ,IAAI;AACvC,gBAAU,UAAU;AACpB,gBAAU,MAAM;AAAA,IACpB,CAAC;AAAA,EACL;AACA,SAAO;AACX;AACA,eAAe,0BAA0B,OAAO;AAC5C,MAAI;AACA,UAAMC,QAAO,cAAc;AAC3B,UAAM,SAAS,MAAMA,MAAK;AAC1B,QAAI,CAAC;AACD;AACJ,UAAM,EAAE,MAAM,KAAK,IAAI;AACvB,oBAAgB,OAAO,KAAK,MAAM,IAAI,CAAC;AACvC,iBAAa,+BAA+B,KAAK,IAAI,IAAI;AAAA,EAC7D,SACO,OAAO;AACV,iBAAa,6EAA6E,OAAO;AACjG,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,SAAS,gBAAgB,OAAO,OAAO;AACnC,aAAW,OAAO,OAAO;AACrB,UAAM,aAAa,MAAM,MAAM,MAAM,GAAG;AAExC,QAAI,YAAY;AACZ,aAAO,OAAO,YAAY,MAAM,GAAG,CAAC;AAAA,IACxC,OACK;AAED,YAAM,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG;AAAA,IACtC;AAAA,EACJ;AACJ;AAEA,SAAS,cAAc,SAAS;AAC5B,SAAO;AAAA,IACH,SAAS;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAM,mBAAmB;AACzB,IAAM,gBAAgB;AACtB,SAAS,4BAA4B,OAAO;AACxC,SAAO,QAAQ,KAAK,IACd;AAAA,IACE,IAAI;AAAA,IACJ,OAAO;AAAA,EACX,IACE;AAAA,IACE,IAAI,MAAM;AAAA,IACV,OAAO,MAAM;AAAA,EACjB;AACR;AACA,SAAS,6BAA6B,OAAO;AACzC,MAAI,QAAQ,KAAK,GAAG;AAChB,UAAM,aAAa,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC;AAC7C,UAAM,WAAW,MAAM;AACvB,UAAMC,SAAQ;AAAA,MACV,OAAO,WAAW,IAAI,CAAC,aAAa;AAAA,QAChC,UAAU;AAAA,QACV,KAAK;AAAA,QACL,OAAO,MAAM,MAAM,MAAM,OAAO;AAAA,MACpC,EAAE;AAAA,MACF,SAAS,WACJ,OAAO,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,QAAQ,EACxC,IAAI,CAAC,OAAO;AACb,cAAMC,SAAQ,SAAS,IAAI,EAAE;AAC7B,eAAO;AAAA,UACH,UAAU;AAAA,UACV,KAAK;AAAA,UACL,OAAOA,OAAM,SAAS,OAAO,CAAC,SAAS,QAAQ;AAC3C,oBAAQ,GAAG,IAAIA,OAAM,GAAG;AACxB,mBAAO;AAAA,UACX,GAAG,CAAC,CAAC;AAAA,QACT;AAAA,MACJ,CAAC;AAAA,IACL;AACA,WAAOD;AAAA,EACX;AACA,QAAM,QAAQ;AAAA,IACV,OAAO,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI,CAAC,SAAS;AAAA,MAC3C,UAAU;AAAA,MACV;AAAA,MACA,OAAO,MAAM,OAAO,GAAG;AAAA,IAC3B,EAAE;AAAA,EACN;AAEA,MAAI,MAAM,YAAY,MAAM,SAAS,QAAQ;AACzC,UAAM,UAAU,MAAM,SAAS,IAAI,CAAC,gBAAgB;AAAA,MAChD,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM,UAAU;AAAA,IAC3B,EAAE;AAAA,EACN;AACA,MAAI,MAAM,kBAAkB,MAAM;AAC9B,UAAM,mBAAmB,MAAM,KAAK,MAAM,iBAAiB,EAAE,IAAI,CAAC,SAAS;AAAA,MACvE,UAAU;AAAA,MACV;AAAA,MACA,OAAO,MAAM,GAAG;AAAA,IACpB,EAAE;AAAA,EACN;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,QAAQ;AAC7B,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,MAAI,MAAM,QAAQ,MAAM,GAAG;AAEvB,WAAO,OAAO,OAAO,CAAC,MAAM,UAAU;AAClC,WAAK,KAAK,KAAK,MAAM,GAAG;AACxB,WAAK,WAAW,KAAK,MAAM,IAAI;AAC/B,WAAK,SAAS,MAAM,GAAG,IAAI,MAAM;AACjC,WAAK,SAAS,MAAM,GAAG,IAAI,MAAM;AACjC,aAAO;AAAA,IACX,GAAG;AAAA,MACC,UAAU,CAAC;AAAA,MACX,MAAM,CAAC;AAAA,MACP,YAAY,CAAC;AAAA,MACb,UAAU,CAAC;AAAA,IACf,CAAC;AAAA,EACL,OACK;AACD,WAAO;AAAA,MACH,WAAW,cAAc,OAAO,IAAI;AAAA,MACpC,KAAK,cAAc,OAAO,GAAG;AAAA,MAC7B,UAAU,OAAO;AAAA,MACjB,UAAU,OAAO;AAAA,IACrB;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,MAAM;AAC9B,UAAQ,MAAM;AAAA,IACV,KAAK,aAAa;AACd,aAAO;AAAA,IACX,KAAK,aAAa;AACd,aAAO;AAAA,IACX,KAAK,aAAa;AACd,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AAGA,IAAI,mBAAmB;AACvB,IAAM,sBAAsB,CAAC;AAC7B,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AACrB,IAAM,EAAE,QAAQ,SAAS,IAAI;AAO7B,IAAM,eAAe,CAAC,OAAO,QAAQ;AAQrC,SAAS,sBAAsB,KAAK,OAAO;AACvC,sBAAoB;AAAA,IAChB,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACJ,GAAG,CAAC,QAAQ;AACR,QAAI,OAAO,IAAI,QAAQ,YAAY;AAC/B,mBAAa,yMAAyM;AAAA,IAC1N;AACA,QAAI,iBAAiB;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,OAAO;AAAA,IACX,CAAC;AACD,QAAI,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,uBAAuB;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,MAAM;AACV,kCAAsB,KAAK;AAAA,UAC/B;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,YAAY;AAChB,kBAAM,uBAAuB,KAAK;AAClC,gBAAI,kBAAkB,YAAY;AAClC,gBAAI,mBAAmB,YAAY;AAAA,UACvC;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,MAAM;AACV,kCAAsB,KAAK;AAAA,UAC/B;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA;AAAA,UACI,MAAM;AAAA,UACN,QAAQ,YAAY;AAChB,kBAAM,0BAA0B,KAAK;AACrC,gBAAI,kBAAkB,YAAY;AAClC,gBAAI,mBAAmB,YAAY;AAAA,UACvC;AAAA,UACA,SAAS;AAAA,QACb;AAAA,MACJ;AAAA,MACA,aAAa;AAAA,QACT;AAAA,UACI,MAAM;AAAA,UACN,SAAS;AAAA,UACT,QAAQ,CAAC,WAAW;AAChB,kBAAM,QAAQ,MAAM,GAAG,IAAI,MAAM;AACjC,gBAAI,CAAC,OAAO;AACR,2BAAa,iBAAiB,MAAM,oCAAoC,MAAM;AAAA,YAClF,WACS,OAAO,MAAM,WAAW,YAAY;AACzC,2BAAa,iBAAiB,MAAM,kEAAkE,MAAM;AAAA,YAChH,OACK;AACD,oBAAM,OAAO;AACb,2BAAa,UAAU,MAAM,UAAU;AAAA,YAC3C;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,iBAAiB,CAAC,SAAS,QAAQ;AACtC,YAAM,QAAS,QAAQ,qBACnB,QAAQ,kBAAkB;AAC9B,UAAI,SAAS,MAAM,UAAU;AACzB,cAAM,cAAc,QAAQ,kBAAkB,MAAM;AACpD,eAAO,OAAO,WAAW,EAAE,QAAQ,CAAC,UAAU;AAC1C,kBAAQ,aAAa,MAAM,KAAK;AAAA,YAC5B,MAAM,aAAa,MAAM,GAAG;AAAA,YAC5B,KAAK;AAAA,YACL,UAAU;AAAA,YACV,OAAO,MAAM,gBACP;AAAA,cACE,SAAS;AAAA,gBACL,OAAO,MAAM,MAAM,MAAM;AAAA,gBACzB,SAAS;AAAA,kBACL;AAAA,oBACI,MAAM;AAAA,oBACN,SAAS;AAAA,oBACT,QAAQ,MAAM,MAAM,OAAO;AAAA,kBAC/B;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ;AAAA;AAAA,cAEI,OAAO,KAAK,MAAM,MAAM,EAAE,OAAO,CAAC,OAAO,QAAQ;AAC7C,sBAAM,GAAG,IAAI,MAAM,OAAO,GAAG;AAC7B,uBAAO;AAAA,cACX,GAAG,CAAC,CAAC;AAAA;AAAA,UACjB,CAAC;AACD,cAAI,MAAM,YAAY,MAAM,SAAS,QAAQ;AACzC,oBAAQ,aAAa,MAAM,KAAK;AAAA,cAC5B,MAAM,aAAa,MAAM,GAAG;AAAA,cAC5B,KAAK;AAAA,cACL,UAAU;AAAA,cACV,OAAO,MAAM,SAAS,OAAO,CAAC,SAAS,QAAQ;AAC3C,oBAAI;AACA,0BAAQ,GAAG,IAAI,MAAM,GAAG;AAAA,gBAC5B,SACO,OAAO;AAEV,0BAAQ,GAAG,IAAI;AAAA,gBACnB;AACA,uBAAO;AAAA,cACX,GAAG,CAAC,CAAC;AAAA,YACT,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,iBAAiB,CAAC,YAAY;AACjC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,cAAc;AAC7D,YAAI,SAAS,CAAC,KAAK;AACnB,iBAAS,OAAO,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,CAAC,CAAC;AACpD,gBAAQ,aAAa,QAAQ,SACvB,OAAO,OAAO,CAAC,UAAU,SAAS,QAC9B,MAAM,IACH,YAAY,EACZ,SAAS,QAAQ,OAAO,YAAY,CAAC,IACxC,iBAAiB,YAAY,EAAE,SAAS,QAAQ,OAAO,YAAY,CAAC,CAAC,IACzE,QAAQ,IAAI,2BAA2B;AAAA,MACjD;AAAA,IACJ,CAAC;AAED,eAAW,SAAS;AACpB,QAAI,GAAG,kBAAkB,CAAC,YAAY;AAClC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,cAAc;AAC7D,cAAM,iBAAiB,QAAQ,WAAW,gBACpC,QACA,MAAM,GAAG,IAAI,QAAQ,MAAM;AACjC,YAAI,CAAC,gBAAgB;AAGjB;AAAA,QACJ;AACA,YAAI,gBAAgB;AAEhB,cAAI,QAAQ,WAAW;AACnB,uBAAW,SAAS,MAAM,cAAc;AAC5C,kBAAQ,QAAQ,6BAA6B,cAAc;AAAA,QAC/D;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,mBAAmB,CAAC,SAAS,QAAQ;AACxC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,cAAc;AAC7D,cAAM,iBAAiB,QAAQ,WAAW,gBACpC,QACA,MAAM,GAAG,IAAI,QAAQ,MAAM;AACjC,YAAI,CAAC,gBAAgB;AACjB,iBAAO,aAAa,UAAU,QAAQ,MAAM,eAAe,OAAO;AAAA,QACtE;AACA,cAAM,EAAE,KAAK,IAAI;AACjB,YAAI,CAAC,QAAQ,cAAc,GAAG;AAE1B,cAAI,KAAK,WAAW,KAChB,CAAC,eAAe,kBAAkB,IAAI,KAAK,CAAC,CAAC,KAC7C,KAAK,CAAC,KAAK,eAAe,QAAQ;AAClC,iBAAK,QAAQ,QAAQ;AAAA,UACzB;AAAA,QACJ,OACK;AAED,eAAK,QAAQ,OAAO;AAAA,QACxB;AACA,2BAAmB;AACnB,gBAAQ,IAAI,gBAAgB,MAAM,QAAQ,MAAM,KAAK;AACrD,2BAAmB;AAAA,MACvB;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,mBAAmB,CAAC,YAAY;AACnC,UAAI,QAAQ,KAAK,WAAW,IAAI,GAAG;AAC/B,cAAM,UAAU,QAAQ,KAAK,QAAQ,UAAU,EAAE;AACjD,cAAM,QAAQ,MAAM,GAAG,IAAI,OAAO;AAClC,YAAI,CAAC,OAAO;AACR,iBAAO,aAAa,UAAU,OAAO,eAAe,OAAO;AAAA,QAC/D;AACA,cAAM,EAAE,KAAK,IAAI;AACjB,YAAI,KAAK,CAAC,MAAM,SAAS;AACrB,iBAAO,aAAa,2BAA2B,OAAO;AAAA,EAAO,IAAI;AAAA,4BAA+B;AAAA,QACpG;AAGA,aAAK,CAAC,IAAI;AACV,2BAAmB;AACnB,gBAAQ,IAAI,OAAO,MAAM,QAAQ,MAAM,KAAK;AAC5C,2BAAmB;AAAA,MACvB;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,mBAAmB,KAAK,OAAO;AACpC,MAAI,CAAC,oBAAoB,SAAS,aAAa,MAAM,GAAG,CAAC,GAAG;AACxD,wBAAoB,KAAK,aAAa,MAAM,GAAG,CAAC;AAAA,EACpD;AACA,sBAAoB;AAAA,IAChB,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,UAAU;AAAA,MACN,iBAAiB;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,QACN,cAAc;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMJ;AAAA,EACJ,GAAG,CAAC,QAAQ;AAER,UAAM,MAAM,OAAO,IAAI,QAAQ,aAAa,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK;AACrE,UAAM,UAAU,CAAC,EAAE,OAAO,SAAS,MAAM,KAAK,MAAM;AAChD,YAAM,UAAU;AAChB,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,MAAM,IAAI;AAAA,UACV,OAAO,QAAQ;AAAA,UACf,UAAU;AAAA,UACV,MAAM;AAAA,YACF,OAAO,cAAc,MAAM,GAAG;AAAA,YAC9B,QAAQ,cAAc,IAAI;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA;AAAA,QACJ;AAAA,MACJ,CAAC;AACD,YAAM,CAAC,WAAW;AACd,uBAAe;AACf,YAAI,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,YACH,MAAM,IAAI;AAAA,YACV,OAAO,QAAQ;AAAA,YACf,UAAU;AAAA,YACV,MAAM;AAAA,cACF,OAAO,cAAc,MAAM,GAAG;AAAA,cAC9B,QAAQ,cAAc,IAAI;AAAA,cAC1B;AAAA,cACA;AAAA,YACJ;AAAA,YACA;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AACD,cAAQ,CAAC,UAAU;AACf,uBAAe;AACf,YAAI,iBAAiB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,YACH,MAAM,IAAI;AAAA,YACV,SAAS;AAAA,YACT,OAAO,QAAQ;AAAA,YACf,UAAU;AAAA,YACV,MAAM;AAAA,cACF,OAAO,cAAc,MAAM,GAAG;AAAA,cAC9B,QAAQ,cAAc,IAAI;AAAA,cAC1B;AAAA,cACA;AAAA,YACJ;AAAA,YACA;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL,GAAG,IAAI;AACP,UAAM,kBAAkB,QAAQ,CAAC,SAAS;AACtC,YAAM,MAAM,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,UAAU,aAAa;AACpD,YAAI,sBAAsB;AAC1B,YAAI,mBAAmB,YAAY;AACnC,YAAI,kBAAkB;AAClB,cAAI,iBAAiB;AAAA,YACjB,SAAS;AAAA,YACT,OAAO;AAAA,cACH,MAAM,IAAI;AAAA,cACV,OAAO;AAAA,cACP,UAAU;AAAA,cACV,MAAM;AAAA,gBACF;AAAA,gBACA;AAAA,cACJ;AAAA,cACA,SAAS;AAAA,YACb;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,EAAE,MAAM,KAAK,CAAC;AAAA,IACrB,CAAC;AACD,UAAM,WAAW,CAAC,EAAE,QAAQ,KAAK,GAAG,UAAU;AAC1C,UAAI,sBAAsB;AAC1B,UAAI,mBAAmB,YAAY;AACnC,UAAI,CAAC;AACD;AAEJ,YAAM,YAAY;AAAA,QACd,MAAM,IAAI;AAAA,QACV,OAAO,mBAAmB,IAAI;AAAA,QAC9B,MAAM,SAAS,EAAE,OAAO,cAAc,MAAM,GAAG,EAAE,GAAG,gBAAgB,MAAM,CAAC;AAAA,QAC3E,SAAS;AAAA,MACb;AACA,UAAI,SAAS,aAAa,eAAe;AACrC,kBAAU,WAAW;AAAA,MACzB,WACS,SAAS,aAAa,aAAa;AACxC,kBAAU,WAAW;AAAA,MACzB,WACS,UAAU,CAAC,MAAM,QAAQ,MAAM,GAAG;AACvC,kBAAU,WAAW,OAAO;AAAA,MAChC;AACA,UAAI,QAAQ;AACR,kBAAU,KAAK,aAAa,IAAI;AAAA,UAC5B,SAAS;AAAA,YACL,SAAS;AAAA,YACT,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,MACX,CAAC;AAAA,IACL,GAAG,EAAE,UAAU,MAAM,OAAO,OAAO,CAAC;AACpC,UAAM,YAAY,MAAM;AACxB,UAAM,aAAa,QAAQ,CAAC,aAAa;AACrC,gBAAU,QAAQ;AAClB,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,MAAM,IAAI;AAAA,UACV,OAAO,QAAQ,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,MAAM;AAAA,YACF,OAAO,cAAc,MAAM,GAAG;AAAA,YAC9B,MAAM,cAAc,YAAY;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,UAAI,sBAAsB;AAC1B,UAAI,kBAAkB,YAAY;AAClC,UAAI,mBAAmB,YAAY;AAAA,IACvC,CAAC;AACD,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,WAAW,MAAM;AACnB,eAAS;AACT,UAAI,sBAAsB;AAC1B,UAAI,kBAAkB,YAAY;AAClC,UAAI,mBAAmB,YAAY;AACnC,UAAI,YAAY,EAAE,mBACd,aAAa,aAAa,MAAM,GAAG,YAAY;AAAA,IACvD;AAEA,QAAI,sBAAsB;AAC1B,QAAI,kBAAkB,YAAY;AAClC,QAAI,mBAAmB,YAAY;AACnC,QAAI,YAAY,EAAE,mBACd,aAAa,IAAI,MAAM,GAAG,sBAAsB;AAAA,EACxD,CAAC;AACL;AACA,IAAI,kBAAkB;AACtB,IAAI;AASJ,SAAS,uBAAuB,OAAO,aAAa,eAAe;AAE/D,QAAM,UAAU,YAAY,OAAO,CAAC,cAAc,eAAe;AAE7D,iBAAa,UAAU,IAAI,MAAM,KAAK,EAAE,UAAU;AAClD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,aAAW,cAAc,SAAS;AAC9B,UAAM,UAAU,IAAI,WAAY;AAE5B,YAAM,YAAY;AAClB,YAAM,eAAe,gBACf,IAAI,MAAM,OAAO;AAAA,QACf,OAAO,MAAM;AACT,yBAAe;AACf,iBAAO,QAAQ,IAAI,GAAG,IAAI;AAAA,QAC9B;AAAA,QACA,OAAO,MAAM;AACT,yBAAe;AACf,iBAAO,QAAQ,IAAI,GAAG,IAAI;AAAA,QAC9B;AAAA,MACJ,CAAC,IACC;AAEN,qBAAe;AACf,YAAM,WAAW,QAAQ,UAAU,EAAE,MAAM,cAAc,SAAS;AAElE,qBAAe;AACf,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAIA,SAAS,eAAe,EAAE,KAAK,OAAO,QAAQ,GAAG;AAE7C,MAAI,MAAM,IAAI,WAAW,QAAQ,GAAG;AAChC;AAAA,EACJ;AAEA,QAAM,gBAAgB,CAAC,CAAC,QAAQ;AAEhC,MAAI,CAAC,MAAM,GAAG,UAAU;AACpB,2BAAuB,OAAO,OAAO,KAAK,QAAQ,OAAO,GAAG,MAAM,aAAa;AAE/E,UAAM,oBAAoB,MAAM;AAChC,UAAM,KAAK,EAAE,aAAa,SAAU,UAAU;AAC1C,wBAAkB,MAAM,MAAM,SAAS;AACvC,6BAAuB,OAAO,OAAO,KAAK,SAAS,YAAY,OAAO,GAAG,CAAC,CAAC,MAAM,aAAa;AAAA,IAClG;AAAA,EACJ;AACA;AAAA,IAAmB;AAAA;AAAA,IAEnB;AAAA,EAAK;AACT;AAKA,SAAS,cAAc;AACnB,QAAM,QAAQ,YAAY,IAAI;AAG9B,QAAM,QAAQ,MAAM,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC;AACrC,MAAI,KAAK,CAAC;AAEV,MAAI,gBAAgB,CAAC;AACrB,QAAM,QAAQ,QAAQ;AAAA,IAClB,QAAQ,KAAK;AAGT,qBAAe,KAAK;AACpB,UAAI,CAAC,QAAQ;AACT,cAAM,KAAK;AACX,YAAI,QAAQ,aAAa,KAAK;AAC9B,YAAI,OAAO,iBAAiB,SAAS;AAErC,YAAoK,WAAW;AAC3K,gCAAsB,KAAK,KAAK;AAAA,QACpC;AACA,sBAAc,QAAQ,CAAC,WAAW,GAAG,KAAK,MAAM,CAAC;AACjD,wBAAgB,CAAC;AAAA,MACrB;AAAA,IACJ;AAAA,IACA,IAAI,QAAQ;AACR,UAAI,CAAC,KAAK,MAAM,CAAC,QAAQ;AACrB,sBAAc,KAAK,MAAM;AAAA,MAC7B,OACK;AACD,WAAG,KAAK,MAAM;AAAA,MAClB;AACA,aAAO;AAAA,IACX;AAAA,IACA;AAAA;AAAA;AAAA,IAGA,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI,oBAAI,IAAI;AAAA,IACZ;AAAA,EACJ,CAAC;AAGD,MAAoK,aAAa,OAAO,UAAU,aAAa;AAC3M,UAAM,IAAI,cAAc;AAAA,EAC5B;AACA,SAAO;AACX;AAQA,SAAS,aAAa,OAAO;AACzB,QAAM,GAAG,KAAK;AACd,QAAM,GAAG,MAAM;AACf,QAAM,GAAG,OAAO,CAAC;AACjB,QAAM,MAAM,QAAQ,CAAC;AAErB,QAAM,KAAK;AACf;AAQA,IAAM,aAAa,CAAC,OAAO;AACvB,SAAO,OAAO,OAAO,cAAc,OAAO,GAAG,QAAQ;AACzD;AAUA,SAAS,YAAY,UAAU,UAAU;AAErC,aAAW,OAAO,UAAU;AACxB,UAAM,WAAW,SAAS,GAAG;AAE7B,QAAI,EAAE,OAAO,WAAW;AACpB;AAAA,IACJ;AACA,UAAM,cAAc,SAAS,GAAG;AAChC,QAAI,cAAc,WAAW,KACzB,cAAc,QAAQ,KACtB,CAAC,MAAM,QAAQ,KACf,CAAC,WAAW,QAAQ,GAAG;AACvB,eAAS,GAAG,IAAI,YAAY,aAAa,QAAQ;AAAA,IACrD,OACK;AAGD,UAAI,QAAQ;AACR,YAAI,UAAU,KAAK,QAAQ;AAAA,MAC/B,OACK;AACD,iBAAS,GAAG,IAAI;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAeA,SAAS,gBAAgB,iBAAiB,KAAK;AAE3C,MAAI,OAA0C;AAC1C,WAAO,MAAM;AAAA,IAAE;AAAA,EACnB;AACA,SAAO,CAAC,cAAc;AAClB,UAAM,QAAQ,IAAI,KAAK,SAAS,gBAAgB;AAChD,QAAI,CAAC,OAAO;AAER;AAAA,IACJ;AAEA,QAAI,KAAK,QAAQ;AAEjB,eAAW,cAAc,WAAW;AAChC,YAAM,WAAW,UAAU,UAAU;AAErC,UAAI,WAAW,QAAQ,KAAK,MAAM,GAAG,IAAI,SAAS,GAAG,GAAG;AAEpD,cAAM,KAAK,SAAS;AACpB,YAAI,OAAO,gBAAgB,KAAK;AAC5B,kBAAQ,KAAK,qCAAqC,gBAAgB,GAAG,SAAS,EAAE,eAAe;AAE/F,iBAAO,IAAI,WAAW;AAAA,QAC1B;AACA,cAAM,gBAAgB,MAAM,GAAG,IAAI,EAAE;AACrC,YAAI,CAAC,eAAe;AAChB,kBAAQ,IAAI,uDAAuD;AACnE;AAAA,QACJ;AACA,iBAAS,OAAO,aAAa;AAAA,MACjC;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,OAAO,MAAM;AAAE;AACrB,SAAS,gBAAgB,eAAe,UAAU,UAAU,YAAY,MAAM;AAC1E,gBAAc,KAAK,QAAQ;AAC3B,QAAM,qBAAqB,MAAM;AAC7B,UAAM,MAAM,cAAc,QAAQ,QAAQ;AAC1C,QAAI,MAAM,IAAI;AACV,oBAAc,OAAO,KAAK,CAAC;AAC3B,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,MAAI,CAAC,YAAY,gBAAgB,GAAG;AAChC,mBAAe,kBAAkB;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,kBAAkB,MAAM;AAClD,gBAAc,MAAM,EAAE,QAAQ,CAAC,aAAa;AACxC,aAAS,GAAG,IAAI;AAAA,EACpB,CAAC;AACL;AAEA,IAAM,yBAAyB,CAAC,OAAO,GAAG;AAK1C,IAAM,gBAAgB,OAAO;AAK7B,IAAM,cAAc,OAAO;AAC3B,SAAS,qBAAqB,QAAQ,cAAc;AAEhD,MAAI,kBAAkB,OAAO,wBAAwB,KAAK;AACtD,iBAAa,QAAQ,CAAC,OAAO,QAAQ,OAAO,IAAI,KAAK,KAAK,CAAC;AAAA,EAC/D,WACS,kBAAkB,OAAO,wBAAwB,KAAK;AAE3D,iBAAa,QAAQ,OAAO,KAAK,MAAM;AAAA,EAC3C;AAEA,aAAW,OAAO,cAAc;AAC5B,QAAI,CAAC,aAAa,eAAe,GAAG;AAChC;AACJ,UAAM,WAAW,aAAa,GAAG;AACjC,UAAM,cAAc,OAAO,GAAG;AAC9B,QAAI,cAAc,WAAW,KACzB,cAAc,QAAQ,KACtB,OAAO,eAAe,GAAG,KACzB,CAAC,MAAM,QAAQ,KACf,CAAC,WAAW,QAAQ,GAAG;AAIvB,aAAO,GAAG,IAAI,qBAAqB,aAAa,QAAQ;AAAA,IAC5D,OACK;AAED,aAAO,GAAG,IAAI;AAAA,IAClB;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,oBAAqB,OACrB,OAAO,qBAAqB;AAAA;AAAA,EACD,OAAO;AAAA;AAQxC,SAAS,YAAY,KAAK;AACtB,SAAO,OAAO,eAAe,KAAK,mBAAmB,CAAC,CAAC;AAC3D;AAOA,SAAS,cAAc,KAAK;AACxB,SAAO,CAAC,cAAc,GAAG,KAAK,CAAC,IAAI,eAAe,iBAAiB;AACvE;AACA,IAAM,EAAE,OAAO,IAAI;AACnB,SAAS,WAAW,GAAG;AACnB,SAAO,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE;AAC5B;AACA,SAAS,mBAAmB,IAAI,SAAS,OAAO,KAAK;AACjD,QAAM,EAAE,OAAO,SAAS,QAAQ,IAAI;AACpC,QAAM,eAAe,MAAM,MAAM,MAAM,EAAE;AACzC,MAAI;AACJ,WAAS,QAAQ;AACb,QAAI,CAAC,gBAA6D,CAAC,KAAM;AAErE,UAAI,QAAQ;AACR,YAAI,MAAM,MAAM,OAAO,IAAI,QAAQ,MAAM,IAAI,CAAC,CAAC;AAAA,MACnD,OACK;AACD,cAAM,MAAM,MAAM,EAAE,IAAI,QAAQ,MAAM,IAAI,CAAC;AAAA,MAC/C;AAAA,IACJ;AAEA,UAAM,aAAwD;AAAA;AAAA,MAEtD,OAAO,IAAI,QAAQ,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK;AAAA,QACxC,OAAO,MAAM,MAAM,MAAM,EAAE,CAAC;AAClC,WAAO,OAAO,YAAY,SAAS,OAAO,KAAK,WAAW,CAAC,CAAC,EAAE,OAAO,CAAC,iBAAiB,SAAS;AAC5F,UAA+C,QAAQ,YAAY;AAC/D,gBAAQ,KAAK,uGAAuG,IAAI,eAAe,EAAE,IAAI;AAAA,MACjJ;AACA,sBAAgB,IAAI,IAAI,QAAQ,SAAS,MAAM;AAC3C,uBAAe,KAAK;AAEpB,cAAMC,SAAQ,MAAM,GAAG,IAAI,EAAE;AAG7B,YAAI,UAAU,CAACA,OAAM;AACjB;AAIJ,eAAO,QAAQ,IAAI,EAAE,KAAKA,QAAOA,MAAK;AAAA,MAC1C,CAAC,CAAC;AACF,aAAO;AAAA,IACX,GAAG,CAAC,CAAC,CAAC;AAAA,EACV;AACA,UAAQ,iBAAiB,IAAI,OAAO,SAAS,OAAO,KAAK,IAAI;AAC7D,SAAO;AACX;AACA,SAAS,iBAAiB,KAAK,OAAO,UAAU,CAAC,GAAG,OAAO,KAAK,gBAAgB;AAC5E,MAAI;AACJ,QAAM,mBAAmB,OAAO,EAAE,SAAS,CAAC,EAAE,GAAG,OAAO;AAExD,MAA+C,CAAC,MAAM,GAAG,QAAQ;AAC7D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EACrC;AAEA,QAAM,oBAAoB,EAAE,MAAM,KAAK;AAEvC,MAA+C,CAAC,QAAQ;AACpD,sBAAkB,YAAY,CAAC,UAAU;AAErC,UAAI,aAAa;AACb,yBAAiB;AAAA,MAErB,WACS,eAAe,SAAS,CAAC,MAAM,cAAc;AAGlD,YAAI,MAAM,QAAQ,cAAc,GAAG;AAC/B,yBAAe,KAAK,KAAK;AAAA,QAC7B,OACK;AACD,kBAAQ,MAAM,kFAAkF;AAAA,QACpG;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI;AACJ,MAAI;AACJ,MAAI,gBAAgB,CAAC;AACrB,MAAI,sBAAsB,CAAC;AAC3B,MAAI;AACJ,QAAM,eAAe,MAAM,MAAM,MAAM,GAAG;AAG1C,MAAI,CAAC,kBAAkB,CAAC,gBAA6D,CAAC,KAAM;AAExF,QAAI,QAAQ;AACR,UAAI,MAAM,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,IAClC,OACK;AACD,YAAM,MAAM,MAAM,GAAG,IAAI,CAAC;AAAA,IAC9B;AAAA,EACJ;AACA,QAAM,WAAW,IAAI,CAAC,CAAC;AAGvB,MAAI;AACJ,WAAS,OAAO,uBAAuB;AACnC,QAAI;AACJ,kBAAc,kBAAkB;AAGhC,QAAK,MAAwC;AACzC,uBAAiB,CAAC;AAAA,IACtB;AACA,QAAI,OAAO,0BAA0B,YAAY;AAC7C,4BAAsB,MAAM,MAAM,MAAM,GAAG,CAAC;AAC5C,6BAAuB;AAAA,QACnB,MAAM,aAAa;AAAA,QACnB,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ;AAAA,IACJ,OACK;AACD,2BAAqB,MAAM,MAAM,MAAM,GAAG,GAAG,qBAAqB;AAClE,6BAAuB;AAAA,QACnB,MAAM,aAAa;AAAA,QACnB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,MACZ;AAAA,IACJ;AACA,UAAM,eAAgB,iBAAiB,OAAO;AAC9C,aAAS,EAAE,KAAK,MAAM;AAClB,UAAI,mBAAmB,cAAc;AACjC,sBAAc;AAAA,MAClB;AAAA,IACJ,CAAC;AACD,sBAAkB;AAElB,yBAAqB,eAAe,sBAAsB,MAAM,MAAM,MAAM,GAAG,CAAC;AAAA,EACpF;AACA,QAAM,SAAS,iBACT,SAASC,UAAS;AAChB,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,WAAW,QAAQ,MAAM,IAAI,CAAC;AAEpC,SAAK,OAAO,CAAC,WAAW;AAEpB,aAAO,QAAQ,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACL;AAAA;AAAA,IAEK,OACK,MAAM;AACJ,YAAM,IAAI,MAAM,cAAc,GAAG,oEAAoE;AAAA,IACzG,IACE;AAAA;AACd,WAAS,WAAW;AAChB,UAAM,KAAK;AACX,oBAAgB,CAAC;AACjB,0BAAsB,CAAC;AACvB,UAAM,GAAG,OAAO,GAAG;AAAA,EACvB;AAMA,QAAM,SAAS,CAAC,IAAI,OAAO,OAAO;AAC9B,QAAI,iBAAiB,IAAI;AACrB,SAAG,WAAW,IAAI;AAClB,aAAO;AAAA,IACX;AACA,UAAM,gBAAgB,WAAY;AAC9B,qBAAe,KAAK;AACpB,YAAM,OAAO,MAAM,KAAK,SAAS;AACjC,YAAM,oBAAoB,CAAC;AAC3B,YAAM,sBAAsB,CAAC;AAC7B,eAAS,MAAM,UAAU;AACrB,0BAAkB,KAAK,QAAQ;AAAA,MACnC;AACA,eAAS,QAAQ,UAAU;AACvB,4BAAoB,KAAK,QAAQ;AAAA,MACrC;AAEA,2BAAqB,qBAAqB;AAAA,QACtC;AAAA,QACA,MAAM,cAAc,WAAW;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,UAAI;AACJ,UAAI;AACA,cAAM,GAAG,MAAM,QAAQ,KAAK,QAAQ,MAAM,OAAO,OAAO,IAAI;AAAA,MAEhE,SACO,OAAO;AACV,6BAAqB,qBAAqB,KAAK;AAC/C,cAAM;AAAA,MACV;AACA,UAAI,eAAe,SAAS;AACxB,eAAO,IACF,KAAK,CAAC,UAAU;AACjB,+BAAqB,mBAAmB,KAAK;AAC7C,iBAAO;AAAA,QACX,CAAC,EACI,MAAM,CAAC,UAAU;AAClB,+BAAqB,qBAAqB,KAAK;AAC/C,iBAAO,QAAQ,OAAO,KAAK;AAAA,QAC/B,CAAC;AAAA,MACL;AAEA,2BAAqB,mBAAmB,GAAG;AAC3C,aAAO;AAAA,IACX;AACA,kBAAc,aAAa,IAAI;AAC/B,kBAAc,WAAW,IAAI;AAG7B,WAAO;AAAA,EACX;AACA,QAAM,cAA4B,QAAQ;AAAA,IACtC,SAAS,CAAC;AAAA,IACV,SAAS,CAAC;AAAA,IACV,OAAO,CAAC;AAAA,IACR;AAAA,EACJ,CAAC;AACD,QAAM,eAAe;AAAA,IACjB,IAAI;AAAA;AAAA,IAEJ;AAAA,IACA,WAAW,gBAAgB,KAAK,MAAM,mBAAmB;AAAA,IACzD;AAAA,IACA;AAAA,IACA,WAAW,UAAUC,WAAU,CAAC,GAAG;AAC/B,YAAM,qBAAqB,gBAAgB,eAAe,UAAUA,SAAQ,UAAU,MAAM,YAAY,CAAC;AACzG,YAAM,cAAc,MAAM,IAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,GAAG,GAAG,CAAC,UAAU;AAC/E,YAAIA,SAAQ,UAAU,SAAS,kBAAkB,aAAa;AAC1D,mBAAS;AAAA,YACL,SAAS;AAAA,YACT,MAAM,aAAa;AAAA,YACnB,QAAQ;AAAA,UACZ,GAAG,KAAK;AAAA,QACZ;AAAA,MACJ,GAAG,OAAO,CAAC,GAAG,mBAAmBA,QAAO,CAAC,CAAC;AAC1C,aAAO;AAAA,IACX;AAAA,IACA;AAAA,EACJ;AAEA,MAAI,QAAQ;AAER,iBAAa,KAAK;AAAA,EACtB;AACA,QAAM,QAAQ,SAAU,OAClB;AAAA,IAAO;AAAA,MACL;AAAA,MACA,mBAAmB,QAAQ,oBAAI,IAAI,CAAC;AAAA;AAAA,IACxC;AAAA,IAAG;AAAA;AAAA;AAAA,EAGH,IACE,YAAY;AAGlB,QAAM,GAAG,IAAI,KAAK,KAAK;AACvB,QAAM,iBAAkB,MAAM,MAAM,MAAM,GAAG,kBAAmB;AAEhE,QAAM,aAAa,eAAe,MAAM,MAAM,GAAG,IAAI,OAAO,QAAQ,YAAY,GAAG,IAAI,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAEhH,aAAW,OAAO,YAAY;AAC1B,UAAM,OAAO,WAAW,GAAG;AAC3B,QAAK,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,KAAM,WAAW,IAAI,GAAG;AAExD,UAA+C,KAAK;AAChD,YAAI,SAAS,OAAO,KAAK,MAAM,YAAY,GAAG,CAAC;AAAA,MAGnD,WACS,CAAC,gBAAgB;AAEtB,YAAI,gBAAgB,cAAc,IAAI,GAAG;AACrC,cAAI,MAAM,IAAI,GAAG;AACb,iBAAK,QAAQ,aAAa,GAAG;AAAA,UACjC,OACK;AAGD,iCAAqB,MAAM,aAAa,GAAG,CAAC;AAAA,UAChD;AAAA,QACJ;AAGA,YAAI,QAAQ;AACR,cAAI,MAAM,MAAM,MAAM,GAAG,GAAG,KAAK,IAAI;AAAA,QACzC,OACK;AACD,gBAAM,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI;AAAA,QAClC;AAAA,MACJ;AAEA,UAAK,MAAwC;AACzC,oBAAY,MAAM,KAAK,GAAG;AAAA,MAC9B;AAAA,IAEJ,WACS,OAAO,SAAS,YAAY;AACjC,YAAM,cAAyD,MAAM,OAAO,OAAO,MAAM,GAAG;AAI5F,UAAI,QAAQ;AACR,YAAI,YAAY,KAAK,WAAW;AAAA,MACpC,OACK;AAED,mBAAW,GAAG,IAAI;AAAA,MACtB;AAEA,UAAK,MAAwC;AACzC,oBAAY,QAAQ,GAAG,IAAI;AAAA,MAC/B;AAGA,uBAAiB,QAAQ,GAAG,IAAI;AAAA,IACpC,WACU,MAAwC;AAE9C,UAAI,WAAW,IAAI,GAAG;AAClB,oBAAY,QAAQ,GAAG,IAAI;AAAA;AAAA,UAEnB,QAAQ,QAAQ,GAAG;AAAA,YACrB;AACN,YAAI,WAAW;AACX,gBAAM,UAAU,WAAW;AAAA,WAEtB,WAAW,WAAW,QAAQ,CAAC,CAAC;AACrC,kBAAQ,KAAK,GAAG;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAGA,MAAI,QAAQ;AACR,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACrC,UAAI,OAAO,KAAK,WAAW,GAAG,CAAC;AAAA,IACnC,CAAC;AAAA,EACL,OACK;AACD,WAAO,OAAO,UAAU;AAGxB,WAAO,MAAM,KAAK,GAAG,UAAU;AAAA,EACnC;AAIA,SAAO,eAAe,OAAO,UAAU;AAAA,IACnC,KAAK,MAAkD,MAAM,SAAS,QAAQ,MAAM,MAAM,MAAM,GAAG;AAAA,IACnG,KAAK,CAAC,UAAU;AAEZ,UAA+C,KAAK;AAChD,cAAM,IAAI,MAAM,qBAAqB;AAAA,MACzC;AACA,aAAO,CAAC,WAAW;AAEf,eAAO,QAAQ,KAAK;AAAA,MACxB,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AAGD,MAAK,MAAwC;AACzC,UAAM,aAAa,QAAQ,CAAC,aAAa;AACrC,YAAM,eAAe;AACrB,eAAS,YAAY,MAAM,QAAQ,CAAC,aAAa;AAC7C,YAAI,YAAY,MAAM,QAAQ;AAC1B,gBAAM,iBAAiB,SAAS,OAAO,QAAQ;AAC/C,gBAAM,iBAAiB,MAAM,OAAO,QAAQ;AAC5C,cAAI,OAAO,mBAAmB,YAC1B,cAAc,cAAc,KAC5B,cAAc,cAAc,GAAG;AAC/B,wBAAY,gBAAgB,cAAc;AAAA,UAC9C,OACK;AAED,qBAAS,OAAO,QAAQ,IAAI;AAAA,UAChC;AAAA,QACJ;AAGA,YAAI,OAAO,UAAU,MAAM,SAAS,QAAQ,QAAQ,CAAC;AAAA,MACzD,CAAC;AAED,aAAO,KAAK,MAAM,MAAM,EAAE,QAAQ,CAAC,aAAa;AAC5C,YAAI,EAAE,YAAY,SAAS,SAAS;AAChC,cAAI,OAAO,QAAQ;AAAA,QACvB;AAAA,MACJ,CAAC;AAED,oBAAc;AACd,wBAAkB;AAClB,YAAM,MAAM,MAAM,GAAG,IAAI,MAAM,SAAS,aAAa,UAAU;AAC/D,wBAAkB;AAClB,eAAS,EAAE,KAAK,MAAM;AAClB,sBAAc;AAAA,MAClB,CAAC;AACD,iBAAW,cAAc,SAAS,YAAY,SAAS;AACnD,cAAM,WAAW,SAAS,UAAU;AACpC,YAAI,OAAO,YAAY,OAAO,UAAU,UAAU,CAAC;AAAA,MACvD;AAEA,iBAAW,cAAc,SAAS,YAAY,SAAS;AACnD,cAAM,SAAS,SAAS,YAAY,QAAQ,UAAU;AACtD,cAAM,cAAc;AAAA;AAAA,UAEZ,SAAS,MAAM;AACX,2BAAe,KAAK;AACpB,mBAAO,OAAO,KAAK,OAAO,KAAK;AAAA,UACnC,CAAC;AAAA,YACH;AACN,YAAI,OAAO,YAAY,WAAW;AAAA,MACtC;AAEA,aAAO,KAAK,MAAM,YAAY,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpD,YAAI,EAAE,OAAO,SAAS,YAAY,UAAU;AACxC,cAAI,OAAO,GAAG;AAAA,QAClB;AAAA,MACJ,CAAC;AAED,aAAO,KAAK,MAAM,YAAY,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpD,YAAI,EAAE,OAAO,SAAS,YAAY,UAAU;AACxC,cAAI,OAAO,GAAG;AAAA,QAClB;AAAA,MACJ,CAAC;AAED,YAAM,cAAc,SAAS;AAC7B,YAAM,WAAW,SAAS;AAC1B,YAAM,eAAe;AAAA,IACzB,CAAC;AAAA,EACL;AACA,MAAoK,WAAW;AAC3K,UAAM,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,cAAc;AAAA;AAAA,MAEd,YAAY;AAAA,IAChB;AACA,KAAC,MAAM,eAAe,YAAY,mBAAmB,EAAE,QAAQ,CAAC,MAAM;AAClE,aAAO,eAAe,OAAO,GAAG,OAAO,EAAE,OAAO,MAAM,CAAC,EAAE,GAAG,aAAa,CAAC;AAAA,IAC9E,CAAC;AAAA,EACL;AAEA,MAAI,QAAQ;AAER,UAAM,KAAK;AAAA,EACf;AAEA,QAAM,GAAG,QAAQ,CAAC,aAAa;AAE3B,QAAoK,WAAW;AAC3K,YAAM,aAAa,MAAM,IAAI,MAAM,SAAS;AAAA,QACxC;AAAA,QACA,KAAK,MAAM;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACb,CAAC,CAAC;AACF,aAAO,KAAK,cAAc,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,MAAM,kBAAkB,IAAI,GAAG,CAAC;AAC/E,aAAO,OAAO,UAAU;AAAA,IAC5B,OACK;AACD,aAAO,OAAO,MAAM,IAAI,MAAM,SAAS;AAAA,QACnC;AAAA,QACA,KAAK,MAAM;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACb,CAAC,CAAC,CAAC;AAAA,IACP;AAAA,EACJ,CAAC;AACD,MACI,MAAM,UACN,OAAO,MAAM,WAAW,YACxB,OAAO,MAAM,OAAO,gBAAgB,cACpC,CAAC,MAAM,OAAO,YAAY,SAAS,EAAE,SAAS,eAAe,GAAG;AAChE,YAAQ,KAAK;AAAA;AAAA,kBAEU,MAAM,GAAG,IAAI;AAAA,EACxC;AAEA,MAAI,gBACA,kBACA,QAAQ,SAAS;AACjB,YAAQ,QAAQ,MAAM,QAAQ,YAAY;AAAA,EAC9C;AACA,gBAAc;AACd,oBAAkB;AAClB,SAAO;AACX;AAGA,SAAS,YAET,aAAa,OAAO,cAAc;AAC9B,MAAI;AACJ,MAAI;AACJ,QAAM,eAAe,OAAO,UAAU;AACtC,MAAI,OAAO,gBAAgB,UAAU;AACjC,SAAK;AAEL,cAAU,eAAe,eAAe;AAAA,EAC5C,OACK;AACD,cAAU;AACV,SAAK,YAAY;AACjB,QAA+C,OAAO,OAAO,UAAU;AACnE,YAAM,IAAI,MAAM,wEAAwE;AAAA,IAC5F;AAAA,EACJ;AACA,WAAS,SAAS,OAAO,KAAK;AAC1B,UAAM,aAAa,oBAAoB;AACvC;AAAA;AAAA,KAGM,QAA0E,OAAO,WAC9E,aAAa,OAAO,aAAa,IAAI,IAAI;AAClD,QAAI;AACA,qBAAe,KAAK;AACxB,QAA+C,CAAC,aAAa;AACzD,YAAM,IAAI,MAAM;AAAA;AAAA,8BAEmB;AAAA,IACvC;AACA,YAAQ;AACR,QAAI,CAAC,MAAM,GAAG,IAAI,EAAE,GAAG;AAEnB,UAAI,cAAc;AACd,yBAAiB,IAAI,OAAO,SAAS,KAAK;AAAA,MAC9C,OACK;AACD,2BAAmB,IAAI,SAAS,KAAK;AAAA,MACzC;AAEA,UAAK,MAAwC;AAEzC,iBAAS,SAAS;AAAA,MACtB;AAAA,IACJ;AACA,UAAM,QAAQ,MAAM,GAAG,IAAI,EAAE;AAC7B,QAA+C,KAAK;AAChD,YAAM,QAAQ,WAAW;AACzB,YAAM,WAAW,eACX,iBAAiB,OAAO,OAAO,SAAS,OAAO,IAAI,IACnD,mBAAmB,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO,IAAI;AAChE,UAAI,WAAW,QAAQ;AAEvB,aAAO,MAAM,MAAM,MAAM,KAAK;AAC9B,YAAM,GAAG,OAAO,KAAK;AAAA,IACzB;AACA,QAA+C,WAAW;AACtD,YAAM,kBAAkB,mBAAmB;AAE3C,UAAI,mBACA,gBAAgB;AAAA,MAEhB,CAAC,KAAK;AACN,cAAM,KAAK,gBAAgB;AAC3B,cAAM,QAAQ,cAAc,KAAK,GAAG,WAAY,GAAG,WAAW,CAAC;AAC/D,cAAM,EAAE,IAAI;AAAA,MAChB;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AACA,WAAS,MAAM;AACf,SAAO;AACX;AAEA,IAAI,iBAAiB;AAQrB,SAAS,kBAAkB,QACzB;AACE,mBAAiB;AACrB;AAuBA,SAAS,aAAa,QAAQ;AAC1B,MAA+C,MAAM,QAAQ,OAAO,CAAC,CAAC,GAAG;AACrE,YAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,2CAKmC;AAChD,aAAS,OAAO,CAAC;AAAA,EACrB;AACA,SAAO,OAAO,OAAO,CAAC,SAAS,aAAa;AAExC,YAAQ,SAAS,MAAM,cAAc,IAAI,WAAY;AACjD,aAAO,SAAS,KAAK,MAAM;AAAA,IAC/B;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AASA,SAAS,SAAS,UAAU,cAAc;AACtC,SAAO,MAAM,QAAQ,YAAY,IAC3B,aAAa,OAAO,CAAC,SAAS,QAAQ;AACpC,YAAQ,GAAG,IAAI,WAAY;AAEvB,aAAO,SAAS,KAAK,MAAM,EAAE,GAAG;AAAA,IACpC;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,IACH,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,SAAS,QAAQ;AAEjD,YAAQ,GAAG,IAAI,WAAY;AACvB,YAAM,QAAQ,SAAS,KAAK,MAAM;AAClC,YAAM,WAAW,aAAa,GAAG;AAGjC,aAAO,OAAO,aAAa,aACrB,SAAS,KAAK,MAAM,KAAK;AAAA;AAAA,QAEvB,MAAM,QAAQ;AAAA;AAAA,IAC1B;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACb;AAKA,IAAM,aAAa;AASnB,SAAS,WAAW,UAAU,cAAc;AACxC,SAAO,MAAM,QAAQ,YAAY,IAC3B,aAAa,OAAO,CAAC,SAAS,QAAQ;AAEpC,YAAQ,GAAG,IAAI,YAAa,MAAM;AAE9B,aAAO,SAAS,KAAK,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI;AAAA,IAC7C;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,IACH,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,SAAS,QAAQ;AAEjD,YAAQ,GAAG,IAAI,YAAa,MAAM;AAE9B,aAAO,SAAS,KAAK,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,GAAG,IAAI;AAAA,IAC3D;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACb;AASA,SAAS,iBAAiB,UAAU,cAAc;AAC9C,SAAO,MAAM,QAAQ,YAAY,IAC3B,aAAa,OAAO,CAAC,SAAS,QAAQ;AACpC,YAAQ,GAAG,IAAI;AAAA,MACX,MAAM;AACF,eAAO,SAAS,KAAK,MAAM,EAAE,GAAG;AAAA,MACpC;AAAA,MACA,IAAI,OAAO;AACP,eAAQ,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAAA,MACzC;AAAA,IACJ;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,IACH,OAAO,KAAK,YAAY,EAAE,OAAO,CAAC,SAAS,QAAQ;AACjD,YAAQ,GAAG,IAAI;AAAA,MACX,MAAM;AACF,eAAO,SAAS,KAAK,MAAM,EAAE,aAAa,GAAG,CAAC;AAAA,MAClD;AAAA,MACA,IAAI,OAAO;AACP,eAAQ,SAAS,KAAK,MAAM,EAAE,aAAa,GAAG,CAAC,IAAI;AAAA,MACvD;AAAA,IACJ;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACb;AAUA,SAAS,YAAY,OAAO;AAGxB,MAAI,QAAQ;AAER,WAAO,OAAO,KAAK;AAAA,EACvB,OACK;AACD,UAAM,WAAW,MAAM,KAAK;AAC5B,UAAM,OAAO,CAAC;AACd,eAAW,OAAO,UAAU;AACxB,YAAM,QAAQ,SAAS,GAAG;AAG1B,UAAI,MAAM,QAAQ;AAEd,aAAK,GAAG;AAAA,QAEJ,SAAS;AAAA,UACL,KAAK,MAAM,MAAM,GAAG;AAAA,UACpB,IAAIC,QAAO;AACP,kBAAM,GAAG,IAAIA;AAAA,UACjB;AAAA,QACJ,CAAC;AAAA,MACT,WACS,MAAM,KAAK,KAAK,WAAW,KAAK,GAAG;AAExC,aAAK,GAAG;AAAA,QAEJ,MAAM,OAAO,GAAG;AAAA,MACxB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAwBA,IAAM,iBAAiB,SAAU,MAAM;AAGnC,OAAK,MAAM;AAAA,IACP,eAAe;AACX,YAAM,UAAU,KAAK;AACrB,UAAI,QAAQ,OAAO;AACf,cAAM,QAAQ,QAAQ;AAGtB,YAAI,CAAC,KAAK,WAAW;AACjB,gBAAM,eAAe,CAAC;AACtB,iBAAO,eAAe,MAAM,aAAa;AAAA,YACrC,KAAK,MAAM;AAAA,YACX,KAAK,CAAC,MAAM,OAAO,OAAO,cAAc,CAAC;AAAA,UAC7C,CAAC;AAAA,QACL;AACA,aAAK,UAAU,WAAW,IAAI;AAI9B,YAAI,CAAC,KAAK,QAAQ;AACd,eAAK,SAAS;AAAA,QAClB;AACA,cAAM,KAAK;AACX,YAAI,WAAW;AAGX,yBAAe,KAAK;AAAA,QACxB;AACA,YAAoK,WAAW;AAC3K,gCAAsB,MAAM,IAAI,KAAK;AAAA,QACzC;AAAA,MACJ,WACS,CAAC,KAAK,UAAU,QAAQ,UAAU,QAAQ,OAAO,QAAQ;AAC9D,aAAK,SAAS,QAAQ,OAAO;AAAA,MACjC;AAAA,IACJ;AAAA,IACA,YAAY;AACR,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,CAAC;AACL;", "names": ["MutationType", "open", "state", "store", "$reset", "options", "value"]}