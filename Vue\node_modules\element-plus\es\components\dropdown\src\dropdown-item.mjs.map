{"version": 3, "file": "dropdown-item.mjs", "sources": ["../../../../../../packages/components/dropdown/src/dropdown-item.vue"], "sourcesContent": ["<template>\n  <el-dropdown-collection-item\n    :disabled=\"disabled\"\n    :text-value=\"textValue ?? textContent\"\n  >\n    <el-roving-focus-item :focusable=\"!disabled\">\n      <el-dropdown-item-impl\n        v-bind=\"propsAndAttrs\"\n        @pointerleave=\"handlePointerLeave\"\n        @pointermove=\"handlePointerMove\"\n        @clickimpl=\"handleClick\"\n      >\n        <slot />\n      </el-dropdown-item-impl>\n    </el-roving-focus-item>\n  </el-dropdown-collection-item>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  inject,\n  ref,\n  unref,\n} from 'vue'\nimport { ElRovingFocusItem } from '@element-plus/components/roving-focus-group'\nimport { composeEventHandlers, whenMouse } from '@element-plus/utils'\nimport ElDropdownItemImpl from './dropdown-item-impl.vue'\nimport { useDropdown } from './useDropdown'\nimport {\n  ElCollectionItem as ElDropdownCollectionItem,\n  dropdownItemProps,\n} from './dropdown'\nimport { DROPDOWN_INJECTION_KEY } from './tokens'\n\nexport default defineComponent({\n  name: 'ElDropdownItem',\n  components: {\n    ElDropdownCollectionItem,\n    ElRovingFocusItem,\n    ElDropdownItemImpl,\n  },\n  inheritAttrs: false,\n  props: dropdownItemProps,\n  emits: ['pointermove', 'pointerleave', 'click'],\n  setup(props, { emit, attrs }) {\n    const { elDropdown } = useDropdown()\n    const _instance = getCurrentInstance()\n    const itemRef = ref<HTMLElement | null>(null)\n    const textContent = computed(() => unref(itemRef)?.textContent ?? '')\n    const { onItemEnter, onItemLeave } = inject(\n      DROPDOWN_INJECTION_KEY,\n      undefined\n    )!\n\n    const handlePointerMove = composeEventHandlers(\n      (e: PointerEvent) => {\n        emit('pointermove', e)\n        return e.defaultPrevented\n      },\n      whenMouse((e) => {\n        if (props.disabled) {\n          onItemLeave(e)\n          return\n        }\n\n        const target = e.currentTarget as HTMLElement\n        /**\n         * This handles the following scenario:\n         *   when the item contains a form element such as input element\n         *   when the mouse is moving over the element itself which is contained by\n         *   the item, the default focusing logic should be prevented so that\n         *   it won't cause weird action.\n         */\n        if (\n          target === document.activeElement ||\n          target.contains(document.activeElement)\n        ) {\n          return\n        }\n\n        onItemEnter(e)\n        if (!e.defaultPrevented) {\n          target?.focus()\n        }\n      })\n    )\n\n    const handlePointerLeave = composeEventHandlers((e: PointerEvent) => {\n      emit('pointerleave', e)\n      return e.defaultPrevented\n    }, whenMouse(onItemLeave))\n\n    const handleClick = composeEventHandlers(\n      (e: PointerEvent) => {\n        if (props.disabled) {\n          return\n        }\n        emit('click', e)\n        return e.type !== 'keydown' && e.defaultPrevented\n      },\n      (e) => {\n        if (props.disabled) {\n          e.stopImmediatePropagation()\n          return\n        }\n        if (elDropdown?.hideOnClick?.value) {\n          elDropdown.handleClick?.()\n        }\n        elDropdown.commandHandler?.(props.command, _instance, e)\n      }\n    )\n\n    // direct usage of v-bind={ ...$props, ...$attrs } causes type errors\n    const propsAndAttrs = computed(() => ({ ...props, ...attrs }))\n\n    return {\n      handleClick,\n      handlePointerMove,\n      handlePointerLeave,\n      textContent,\n      propsAndAttrs,\n    }\n  },\n})\n</script>\n"], "names": ["ElDropdownCollectionItem", "_resolveComponent", "_createVNode", "_mergeProps", "_withCtx", "_renderSlot"], "mappings": ";;;;;;;;;AAqCA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,gBAAA;AAAA,EACN,UAAY,EAAA;AAAA,8BACVA,gBAAA;AAAA,IACA,iBAAA;AAAA,IACA,kBAAA;AAAA,GACF;AAAA,EACA,YAAc,EAAA,KAAA;AAAA,EACd,KAAO,EAAA,iBAAA;AAAA,EACP,KAAO,EAAA,CAAC,aAAe,EAAA,cAAA,EAAgB,OAAO,CAAA;AAAA,EAC9C,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAM,OAAS,EAAA;AAC5B,IAAM,MAAA,EAAE,UAAW,EAAA,GAAI,WAAY,EAAA,CAAA;AACnC,IAAA,MAAM,YAAY,kBAAmB,EAAA,CAAA;AACrC,IAAM,MAAA,OAAA,GAAU,IAAwB,IAAI,CAAA,CAAA;AAC5C,IAAA,MAAM,cAAc,QAAS,CAAA,MAAM;AACnC,MAAM,IAAA,EAAE,EAAa,EAAA,CAAA;AAAgB,MACnC,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,MAAA,EAAA,WAAA,EAAA,WAAA,EAAA,GAAA,MAAA,CAAA,sBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAEA,IAAA,MAAM,iBAAoB,GAAA,oBAAA,CAAA,CAAA,CAAA,KAAA;AAAA,MACxB,IAAqB,CAAA,aAAA,EAAA,CAAA,CAAA,CAAA;AACnB,MAAA,OAAK,kBAAgB,CAAA;AACrB,KAAA,EAAA,SAAS,CAAA,CAAA,CAAA,KAAA;AAAA,MACX,IAAA,KAAA,CAAA,QAAA,EAAA;AAAA,QACA,WAAiB,CAAA,CAAA,CAAA,CAAA;AACf,QAAA;AACE,OAAA;AACA,MAAA,MAAA,MAAA,GAAA,CAAA,CAAA,aAAA,CAAA;AAAA,MACF,IAAA,MAAA,KAAA,QAAA,CAAA,aAAA,IAAA,MAAA,CAAA,QAAA,CAAA,QAAA,CAAA,aAAA,CAAA,EAAA;AAEA,QAAA;AAQA,OAAA;AAIE,MAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MACF,IAAA,CAAA,CAAA,CAAA,gBAAA,EAAA;AAEA,QAAA,MAAA,IAAA,IAAa,GAAA,KAAA,CAAA,GAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACb,OAAI;AACF,KAAA,CAAA,CAAA,CAAA;AAAc,IAChB,MAAA,kBAAA,GAAA,oBAAA,CAAA,CAAA,CAAA,KAAA;AAAA,MACF,IAAC,CAAA,cAAA,EAAA,CAAA,CAAA,CAAA;AAAA,MACH,OAAA,CAAA,CAAA,gBAAA,CAAA;AAEA,KAAM,EAAA,SAAA,CAAA,WAAA,CAAA,CAAA,CAAqB;AACzB,IAAA,MAAA,kCAAsB,CAAA,CAAA,CAAA,KAAA;AACtB,MAAA,IAAA,KAAS,CAAA,QAAA,EAAA;AAAA,QACE,OAAA;AAEb,OAAA;AAAoB,MAClB,IAAqB,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA;AACnB,MAAA,aAAoB,KAAA,SAAA,IAAA,CAAA,CAAA,gBAAA,CAAA;AAClB,KAAA,EAAA,CAAA,CAAA,KAAA;AAAA,MACF,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AACA,MAAA,IAAA,cAAe,EAAA;AACf,QAAO,CAAA,CAAA,wBAAW,EAAA,CAAA;AAAe,QACnC,OAAA;AAAA,OACC;AACC,MAAA,IAAA,CAAA,eAAoB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,WAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,EAAA;AAClB,QAAA,CAAA,EAAA,GAA2B,UAAA,CAAA,WAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA;AAC3B,OAAA;AAAA,MACF,CAAA,EAAA,GAAA,UAAA,CAAA,cAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,UAAA,EAAA,KAAA,CAAA,OAAA,EAAA,SAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAAI,CAAA,CAAA;AACF,IAAA,MAAA,aAAyB,GAAA,QAAA,CAAA,OAAA,EAAA,GAAA,KAAA,EAAA,GAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AAAA,IAC3B,OAAA;AACA,MAAA,WAAA;AAAuD,MACzD,iBAAA;AAAA,MACF,kBAAA;AAGA,MAAM,WAAA;AAEN,MAAO,aAAA;AAAA,KACL,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACA,SACA,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EACF,IAAA,EAAA,CAAA;AAAA,EACF,MAAA,gCAAA,GAAAC,gBAAA,CAAA,uBAAA,CAAA,CAAA;AACF,EAAC,MAAA,+BAAA,GAAAA,gBAAA,CAAA,sBAAA,CAAA,CAAA;;;;;;2BA/G+B;AAAA,MAbjBC,WAAA,CAAA,+BAAA,EAAA;AAAA,mBACe,CAAA,IAAA,CAAA,QAAA;AAAA,OAAA,EAAA;wBAWH,CAAA,MAAA;AAAA,UAAAA,WAAA,CAAA,gCAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,aAAA,EAAA;AAAA,0BATY,EAAA,IAAA,CAAA,kBAAA;AAAA,YAAA,aAAA,EAAA,IAAA,CAAA,iBAAA;6BAQT,CAAA,WAAA;AAAA,WAAA,CAAA,EAAA;AAND,YACpB,OAAc,EAAAC,OAAA,CAAA,MAAA;AAAA,cACDC,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,aACF,CAAA;AAAA,YAAA,CAAA,EAAA,CAAA;kCAEJ,EAAA,eAAA,EAAA,aAAA,CAAA,CAAA;AAAA,SAAA,CAAA;AAAA,QAAA,CAAA,EAAA,CAAA;;;;;;;;;;"}