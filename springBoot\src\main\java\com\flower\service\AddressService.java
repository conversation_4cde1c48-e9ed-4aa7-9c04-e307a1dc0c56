package com.flower.service;

import com.flower.entity.UserAddress;

import java.util.List;

/**
 * 收货地址服务接口
 */
public interface AddressService {

    /**
     * 获取用户所有收货地址
     * @param userId 用户ID
     * @return 地址列表
     */
    List<UserAddress> getUserAddresses(Long userId);

    /**
     * 根据ID获取地址详情
     * @param id 地址ID
     * @return 地址信息
     */
    UserAddress getAddressById(Long id);

    /**
     * 添加收货地址
     * @param address 地址信息
     * @return 添加后的地址信息
     */
    UserAddress addAddress(UserAddress address);

    /**
     * 更新收货地址
     * @param address 地址信息
     * @return 更新后的地址信息
     */
    UserAddress updateAddress(UserAddress address);

    /**
     * 删除收货地址
     * @param id 地址ID
     * @param userId 用户ID（用于权限验证）
     * @return 删除结果
     */
    Boolean deleteAddress(Long id, Long userId);

    /**
     * 设置默认地址
     * @param id 地址ID
     * @param userId 用户ID（用于权限验证）
     * @return 设置结果
     */
    Boolean setDefaultAddress(Long id, Long userId);

    /**
     * 获取用户默认地址
     * @param userId 用户ID
     * @return 默认地址
     */
    UserAddress getDefaultAddress(Long userId);
}
