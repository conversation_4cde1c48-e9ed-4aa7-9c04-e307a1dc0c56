package com.flower.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordGenerator {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "123456";
        String encodedPassword = encoder.encode(password);
        System.out.println("原密码: " + password);
        System.out.println("加密后: " + encodedPassword);
        
        // 验证密码
        boolean matches = encoder.matches(password, encodedPassword);
        System.out.println("密码验证: " + matches);
        
        // 测试现有的密码哈希
        String existingHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyiE.rV8jruW.Aie7huLhs.6AoG";
        boolean existingMatches = encoder.matches(password, existingHash);
        System.out.println("现有密码验证: " + existingMatches);
    }
}
