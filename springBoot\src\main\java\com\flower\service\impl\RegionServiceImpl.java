package com.flower.service.impl;

import com.flower.entity.Province;
import com.flower.entity.City;
import com.flower.entity.District;
import com.flower.mapper.RegionMapper;
import com.flower.service.RegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.Cacheable;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 地区服务实现类
 */
@Service
public class RegionServiceImpl implements RegionService {
    
    @Autowired
    private RegionMapper regionMapper;
    
    @Override
    @Cacheable(value = "provinces", key = "'all'")
    public List<Province> getAllProvinces() {
        return regionMapper.getAllProvinces();
    }
    
    @Override
    @Cacheable(value = "cities", key = "#provinceCode")
    public List<City> getCitiesByProvinceCode(String provinceCode) {
        return regionMapper.getCitiesByProvinceCode(provinceCode);
    }
    
    @Override
    @Cacheable(value = "districts", key = "#cityCode")
    public List<District> getDistrictsByCityCode(String cityCode) {
        return regionMapper.getDistrictsByCityCode(cityCode);
    }
    
    @Override
    @Cacheable(value = "regionData", key = "'complete'")
    public Map<String, Object> getRegionData() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有省份
        List<Province> provinces = getAllProvinces();
        List<String> provinceNames = provinces.stream()
                .map(Province::getName)
                .collect(Collectors.toList());

        // 将新疆排在第一位
        if (provinceNames.contains("新疆")) {
            provinceNames.remove("新疆");
            provinceNames.add(0, "新疆");
        }
        
        // 构建城市数据结构
        Map<String, List<String>> cities = new HashMap<>();
        Map<String, List<String>> districts = new HashMap<>();
        
        for (Province province : provinces) {
            // 获取该省的所有城市
            List<City> cityList = getCitiesByProvinceCode(province.getCode());
            List<String> cityNames = cityList.stream()
                    .map(City::getName)
                    .collect(Collectors.toList());
            cities.put(province.getName(), cityNames);
            
            // 获取每个城市的区县
            for (City city : cityList) {
                List<District> districtList = getDistrictsByCityCode(city.getCode());
                List<String> districtNames = districtList.stream()
                        .map(District::getName)
                        .collect(Collectors.toList());
                districts.put(city.getName(), districtNames);
            }
        }
        
        result.put("provinces", provinceNames);
        result.put("cities", cities);
        result.put("districts", districts);
        
        return result;
    }
    
    @Override
    public Map<String, String> getRegionCodes(String provinceName, String cityName, String districtName) {
        Map<String, String> codes = new HashMap<>();
        
        // 获取省份代码
        Province province = regionMapper.getProvinceByName(provinceName);
        if (province != null) {
            codes.put("provinceCode", province.getCode());
            
            // 获取城市代码
            City city = regionMapper.getCityByNameAndProvinceCode(cityName, province.getCode());
            if (city != null) {
                codes.put("cityCode", city.getCode());
                
                // 获取区县代码
                District district = regionMapper.getDistrictByNameAndCityCode(districtName, city.getCode());
                if (district != null) {
                    codes.put("districtCode", district.getCode());
                }
            }
        }
        
        return codes;
    }
}
