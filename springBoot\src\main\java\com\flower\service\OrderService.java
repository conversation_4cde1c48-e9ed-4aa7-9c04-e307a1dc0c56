package com.flower.service;

import com.flower.common.PageResult;
import com.flower.entity.Order;
import com.flower.entity.OrderItem;

import java.util.List;

/**
 * 订单服务接口
 */
public interface OrderService {

    /**
     * 从购物车创建订单
     * @param userId 用户ID
     * @param recipientName 收货人姓名
     * @param recipientPhone 收货人电话
     * @param recipientAddress 收货地址
     * @param deliveryNotes 配送备注
     * @param remark 订单备注
     * @return 创建的订单
     */
    Order createOrderFromCart(Long userId, String recipientName, String recipientPhone,
                             String recipientAddress, String deliveryNotes, String remark);

    /**
     * 从指定商品创建订单
     * @param userId 用户ID
     * @param flowerIds 花卉ID列表
     * @param quantities 数量列表
     * @param recipientName 收货人姓名
     * @param recipientPhone 收货人电话
     * @param recipientAddress 收货地址
     * @param deliveryNotes 配送备注
     * @param remark 订单备注
     * @return 创建的订单
     */
    Order createOrder(Long userId, List<Long> flowerIds, List<Integer> quantities,
                     String recipientName, String recipientPhone, String recipientAddress,
                     String deliveryNotes, String remark);

    /**
     * 根据ID获取订单
     * @param orderId 订单ID
     * @return 订单信息
     */
    Order getOrderById(Long orderId);

    /**
     * 根据订单ID获取订单商品列表
     * @param orderId 订单ID
     * @return 订单商品列表
     */
    List<OrderItem> getOrderItems(Long orderId);

    /**
     * 获取用户订单列表
     * @param userId 用户ID
     * @param current 当前页码
     * @param size 每页大小
     * @param status 订单状态（可选）
     * @return 订单列表
     */
    PageResult<Order> getUserOrders(Long userId, Long current, Long size, Integer status);

    /**
     * 更新订单状态
     * @param orderId 订单ID
     * @param status 新状态
     * @return 更新后的订单
     */
    Order updateOrderStatus(Long orderId, Integer status);

    /**
     * 取消订单
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 成功标志
     */
    Boolean cancelOrder(Long orderId, Long userId);

    /**
     * 支付订单（模拟支付）
     * @param orderId 订单ID
     * @param paymentMethod 支付方式
     * @return 成功标志
     */
    Boolean payOrder(Long orderId, String paymentMethod);

    /**
     * 创建直接下单订单（支持自取和配送）
     * @param userId 用户ID
     * @param flowerIds 花卉ID列表
     * @param quantities 数量列表
     * @param deliveryType 配送方式：1外卖配送，2自取
     * @param recipientName 收货人姓名（配送时使用）
     * @param recipientPhone 收货人电话（配送时使用）
     * @param recipientAddress 收货地址（配送时使用）
     * @param deliveryTime 派送时间（配送时使用）
     * @param pickupName 取货人姓名（自取时使用）
     * @param pickupPhone 取货人电话（自取时使用）
     * @param pickupTime 取货时间（自取时使用）
     * @param remark 备注
     * @param backupAddress 备用地址（自取时使用）
     * @return 创建的订单
     */
    Order createDirectOrder(Long userId, List<Long> flowerIds, List<Integer> quantities,
                           Integer deliveryType, String recipientName, String recipientPhone,
                           String recipientAddress, String deliveryTime, String pickupName, String pickupPhone,
                           String pickupTime, String remark, String backupAddress);
}
