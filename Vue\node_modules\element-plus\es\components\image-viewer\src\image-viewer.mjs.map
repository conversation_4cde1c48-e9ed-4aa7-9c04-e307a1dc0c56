{"version": 3, "file": "image-viewer.mjs", "sources": ["../../../../../../packages/components/image-viewer/src/image-viewer.vue"], "sourcesContent": ["<template>\n  <el-teleport to=\"body\" :disabled=\"!teleported\">\n    <transition name=\"viewer-fade\" appear>\n      <div\n        ref=\"wrapper\"\n        :tabindex=\"-1\"\n        :class=\"ns.e('wrapper')\"\n        :style=\"{ zIndex }\"\n      >\n        <el-focus-trap\n          loop\n          trapped\n          :focus-trap-el=\"wrapper\"\n          focus-start-el=\"container\"\n          @focusout-prevented=\"onFocusoutPrevented\"\n          @release-requested=\"onCloseRequested\"\n        >\n          <div :class=\"ns.e('mask')\" @click.self=\"hideOnClickModal && hide()\" />\n\n          <!-- CLOSE -->\n          <span :class=\"[ns.e('btn'), ns.e('close')]\" @click=\"hide\">\n            <el-icon>\n              <Close />\n            </el-icon>\n          </span>\n\n          <!-- ARROW -->\n          <template v-if=\"!isSingle\">\n            <span :class=\"arrowPrevKls\" @click=\"prev\">\n              <el-icon>\n                <ArrowLeft />\n              </el-icon>\n            </span>\n            <span :class=\"arrowNextKls\" @click=\"next\">\n              <el-icon>\n                <ArrowRight />\n              </el-icon>\n            </span>\n          </template>\n          <div\n            v-if=\"$slots.progress || showProgress\"\n            :class=\"[ns.e('btn'), ns.e('progress')]\"\n          >\n            <slot\n              name=\"progress\"\n              :active-index=\"activeIndex\"\n              :total=\"urlList.length\"\n            >\n              {{ progress }}\n            </slot>\n          </div>\n          <!-- ACTIONS -->\n          <div :class=\"[ns.e('btn'), ns.e('actions')]\">\n            <div :class=\"ns.e('actions__inner')\">\n              <slot\n                name=\"toolbar\"\n                :actions=\"handleActions\"\n                :prev=\"prev\"\n                :next=\"next\"\n                :reset=\"toggleMode\"\n                :active-index=\"activeIndex\"\n                :set-active-item=\"setActiveItem\"\n              >\n                <el-icon @click=\"handleActions('zoomOut')\">\n                  <ZoomOut />\n                </el-icon>\n                <el-icon @click=\"handleActions('zoomIn')\">\n                  <ZoomIn />\n                </el-icon>\n                <i :class=\"ns.e('actions__divider')\" />\n                <el-icon @click=\"toggleMode\">\n                  <component :is=\"mode.icon\" />\n                </el-icon>\n                <i :class=\"ns.e('actions__divider')\" />\n                <el-icon @click=\"handleActions('anticlockwise')\">\n                  <RefreshLeft />\n                </el-icon>\n                <el-icon @click=\"handleActions('clockwise')\">\n                  <RefreshRight />\n                </el-icon>\n              </slot>\n            </div>\n          </div>\n          <!-- CANVAS -->\n          <div :class=\"ns.e('canvas')\">\n            <template v-for=\"(url, i) in urlList\" :key=\"i\">\n              <img\n                v-if=\"i === activeIndex\"\n                :ref=\"(el) => (imgRefs[i] = el as HTMLImageElement)\"\n                :src=\"url\"\n                :style=\"imgStyle\"\n                :class=\"ns.e('img')\"\n                :crossorigin=\"crossorigin\"\n                @load=\"handleImgLoad\"\n                @error=\"handleImgError\"\n                @mousedown=\"handleMouseDown\"\n              />\n            </template>\n          </div>\n          <slot />\n        </el-focus-trap>\n      </div>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  effectScope,\n  markRaw,\n  nextTick,\n  onMounted,\n  ref,\n  shallowRef,\n  watch,\n} from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { throttle } from 'lodash-unified'\nimport { useLocale, useNamespace, useZIndex } from '@element-plus/hooks'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { keysOf } from '@element-plus/utils'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport ElTeleport from '@element-plus/components/teleport'\nimport ElIcon from '@element-plus/components/icon'\nimport {\n  ArrowLeft,\n  ArrowRight,\n  Close,\n  FullScreen,\n  RefreshLeft,\n  RefreshRight,\n  ScaleToOriginal,\n  ZoomIn,\n  ZoomOut,\n} from '@element-plus/icons-vue'\nimport { imageViewerEmits, imageViewerProps } from './image-viewer'\n\nimport type { CSSProperties } from 'vue'\nimport type { ImageViewerAction, ImageViewerMode } from './image-viewer'\n\nconst modes: Record<'CONTAIN' | 'ORIGINAL', ImageViewerMode> = {\n  CONTAIN: {\n    name: 'contain',\n    icon: markRaw(FullScreen),\n  },\n  ORIGINAL: {\n    name: 'original',\n    icon: markRaw(ScaleToOriginal),\n  },\n}\n\ndefineOptions({\n  name: 'ElImageViewer',\n})\n\nconst props = defineProps(imageViewerProps)\nconst emit = defineEmits(imageViewerEmits)\n\nlet stopWheelListener: (() => void) | undefined\nlet prevOverflow = ''\n\nconst { t } = useLocale()\nconst ns = useNamespace('image-viewer')\nconst { nextZIndex } = useZIndex()\nconst wrapper = ref<HTMLDivElement>()\nconst imgRefs = ref<HTMLImageElement[]>([])\n\nconst scopeEventListener = effectScope()\n\nconst loading = ref(true)\nconst activeIndex = ref(props.initialIndex)\nconst mode = shallowRef<ImageViewerMode>(modes.CONTAIN)\nconst transform = ref({\n  scale: 1,\n  deg: 0,\n  offsetX: 0,\n  offsetY: 0,\n  enableTransition: false,\n})\nconst zIndex = ref(props.zIndex ?? nextZIndex())\n\nconst isSingle = computed(() => {\n  const { urlList } = props\n  return urlList.length <= 1\n})\n\nconst isFirst = computed(() => activeIndex.value === 0)\n\nconst isLast = computed(() => activeIndex.value === props.urlList.length - 1)\n\nconst currentImg = computed(() => props.urlList[activeIndex.value])\n\nconst arrowPrevKls = computed(() => [\n  ns.e('btn'),\n  ns.e('prev'),\n  ns.is('disabled', !props.infinite && isFirst.value),\n])\n\nconst arrowNextKls = computed(() => [\n  ns.e('btn'),\n  ns.e('next'),\n  ns.is('disabled', !props.infinite && isLast.value),\n])\n\nconst imgStyle = computed(() => {\n  const { scale, deg, offsetX, offsetY, enableTransition } = transform.value\n  let translateX = offsetX / scale\n  let translateY = offsetY / scale\n\n  const radian = (deg * Math.PI) / 180\n  const cosRadian = Math.cos(radian)\n  const sinRadian = Math.sin(radian)\n  translateX = translateX * cosRadian + translateY * sinRadian\n  translateY = translateY * cosRadian - (offsetX / scale) * sinRadian\n\n  const style: CSSProperties = {\n    transform: `scale(${scale}) rotate(${deg}deg) translate(${translateX}px, ${translateY}px)`,\n    transition: enableTransition ? 'transform .3s' : '',\n  }\n  if (mode.value.name === modes.CONTAIN.name) {\n    style.maxWidth = style.maxHeight = '100%'\n  }\n  return style\n})\n\nconst progress = computed(\n  () => `${activeIndex.value + 1} / ${props.urlList.length}`\n)\n\nfunction hide() {\n  unregisterEventListener()\n  stopWheelListener?.()\n  document.body.style.overflow = prevOverflow\n  emit('close')\n}\n\nfunction registerEventListener() {\n  const keydownHandler = throttle((e: KeyboardEvent) => {\n    switch (e.code) {\n      // ESC\n      case EVENT_CODE.esc:\n        props.closeOnPressEscape && hide()\n        break\n      // SPACE\n      case EVENT_CODE.space:\n        toggleMode()\n        break\n      // LEFT_ARROW\n      case EVENT_CODE.left:\n        prev()\n        break\n      // UP_ARROW\n      case EVENT_CODE.up:\n        handleActions('zoomIn')\n        break\n      // RIGHT_ARROW\n      case EVENT_CODE.right:\n        next()\n        break\n      // DOWN_ARROW\n      case EVENT_CODE.down:\n        handleActions('zoomOut')\n        break\n    }\n  })\n  const mousewheelHandler = throttle((e: WheelEvent) => {\n    const delta = e.deltaY || e.deltaX\n    handleActions(delta < 0 ? 'zoomIn' : 'zoomOut', {\n      zoomRate: props.zoomRate,\n      enableTransition: false,\n    })\n  })\n\n  scopeEventListener.run(() => {\n    useEventListener(document, 'keydown', keydownHandler)\n    useEventListener(document, 'wheel', mousewheelHandler)\n  })\n}\n\nfunction unregisterEventListener() {\n  scopeEventListener.stop()\n}\n\nfunction handleImgLoad() {\n  loading.value = false\n}\n\nfunction handleImgError(e: Event) {\n  loading.value = false\n  ;(e.target as HTMLImageElement).alt = t('el.image.error')\n}\n\nfunction handleMouseDown(e: MouseEvent) {\n  if (loading.value || e.button !== 0 || !wrapper.value) return\n  transform.value.enableTransition = false\n\n  const { offsetX, offsetY } = transform.value\n  const startX = e.pageX\n  const startY = e.pageY\n\n  const dragHandler = throttle((ev: MouseEvent) => {\n    transform.value = {\n      ...transform.value,\n      offsetX: offsetX + ev.pageX - startX,\n      offsetY: offsetY + ev.pageY - startY,\n    }\n  })\n  const removeMousemove = useEventListener(document, 'mousemove', dragHandler)\n  useEventListener(document, 'mouseup', () => {\n    removeMousemove()\n  })\n\n  e.preventDefault()\n}\n\nfunction reset() {\n  transform.value = {\n    scale: 1,\n    deg: 0,\n    offsetX: 0,\n    offsetY: 0,\n    enableTransition: false,\n  }\n}\n\nfunction toggleMode() {\n  if (loading.value) return\n\n  const modeNames = keysOf(modes)\n  const modeValues = Object.values(modes)\n  const currentMode = mode.value.name\n  const index = modeValues.findIndex((i) => i.name === currentMode)\n  const nextIndex = (index + 1) % modeNames.length\n  mode.value = modes[modeNames[nextIndex]]\n  reset()\n}\n\nfunction setActiveItem(index: number) {\n  const len = props.urlList.length\n  activeIndex.value = (index + len) % len\n}\n\nfunction prev() {\n  if (isFirst.value && !props.infinite) return\n  setActiveItem(activeIndex.value - 1)\n}\n\nfunction next() {\n  if (isLast.value && !props.infinite) return\n  setActiveItem(activeIndex.value + 1)\n}\n\nfunction handleActions(action: ImageViewerAction, options = {}) {\n  if (loading.value) return\n  const { minScale, maxScale } = props\n  const { zoomRate, rotateDeg, enableTransition } = {\n    zoomRate: props.zoomRate,\n    rotateDeg: 90,\n    enableTransition: true,\n    ...options,\n  }\n  switch (action) {\n    case 'zoomOut':\n      if (transform.value.scale > minScale) {\n        transform.value.scale = Number.parseFloat(\n          (transform.value.scale / zoomRate).toFixed(3)\n        )\n      }\n      break\n    case 'zoomIn':\n      if (transform.value.scale < maxScale) {\n        transform.value.scale = Number.parseFloat(\n          (transform.value.scale * zoomRate).toFixed(3)\n        )\n      }\n      break\n    case 'clockwise':\n      transform.value.deg += rotateDeg\n      emit('rotate', transform.value.deg)\n      break\n    case 'anticlockwise':\n      transform.value.deg -= rotateDeg\n      emit('rotate', transform.value.deg)\n      break\n  }\n  transform.value.enableTransition = enableTransition\n}\n\nfunction onFocusoutPrevented(event: CustomEvent) {\n  if (event.detail?.focusReason === 'pointer') {\n    event.preventDefault()\n  }\n}\n\nfunction onCloseRequested() {\n  if (props.closeOnPressEscape) {\n    hide()\n  }\n}\n\nfunction wheelHandler(e: WheelEvent) {\n  if (!e.ctrlKey) return\n\n  if (e.deltaY < 0) {\n    e.preventDefault()\n    return false\n  } else if (e.deltaY > 0) {\n    e.preventDefault()\n    return false\n  }\n}\n\nwatch(currentImg, () => {\n  nextTick(() => {\n    const $img = imgRefs.value[0]\n    if (!$img?.complete) {\n      loading.value = true\n    }\n  })\n})\n\nwatch(activeIndex, (val) => {\n  reset()\n  emit('switch', val)\n})\n\nonMounted(() => {\n  registerEventListener()\n\n  stopWheelListener = useEventListener('wheel', wheelHandler, {\n    passive: false,\n  })\n\n  // prevent body scroll\n  prevOverflow = document.body.style.overflow\n  document.body.style.overflow = 'hidden'\n})\n\ndefineExpose({\n  /**\n   * @description manually switch image\n   */\n  setActiveItem,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;mCAwJc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAbA,IAAA,MAAM,KAAyD,GAAA,OAAA,CAAA;AAAA,IAAA,MACpD,KAAA,GAAA;AAAA,MAAA,OACD,EAAA;AAAA,QACN,IAAA,EAAM,SAAkB;AAAA,QAC1B,IAAA,EAAA,OAAA,CAAA,UAAA,CAAA;AAAA,OACU;AAAA,MAAA,QACF,EAAA;AAAA,QACN,IAAA,EAAM,UAAuB;AAAA,QAC/B,IAAA,EAAA,OAAA,CAAA,eAAA,CAAA;AAAA,OACF;AASA,KAAI,CAAA;AACJ,IAAA,IAAI,iBAAe,CAAA;AAEnB,IAAM,IAAA,YAAkB,GAAA,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,CAAA,EAAK,cAA2B,CAAA;AACtC,IAAM,MAAA,EAAE,GAAW,YAAI,CAAU,cAAA,CAAA,CAAA;AACjC,IAAA,MAAM,YAA8B,EAAA,GAAA,SAAA,EAAA,CAAA;AACpC,IAAM,MAAA,OAAA,GAAU,GAAwB,EAAC,CAAC;AAE1C,IAAA,MAAM;AAEN,IAAM,MAAA,kBAAkB,GAAA,WAAA,EAAA,CAAA;AACxB,IAAM,MAAA,OAAA,GAAA,GAAA,CAAc,IAAI,CAAA,CAAA;AACxB,IAAM,MAAA,WAAmC,GAAA,GAAA,CAAA,KAAA,CAAM,YAAO,CAAA,CAAA;AACtD,IAAA,MAAM,iBAAgB,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AAAA,IAAA,MACb,SAAA,GAAA,GAAA,CAAA;AAAA,MACP,KAAK,EAAA,CAAA;AAAA,MACL,GAAS,EAAA,CAAA;AAAA,MACT,OAAS,EAAA,CAAA;AAAA,MACT,OAAkB,EAAA,CAAA;AAAA,MACnB,gBAAA,EAAA,KAAA;AACD,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,MAAA,GAAA,eAA0B,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,UAAA,EAAA,CAAA,CAAA;AAC9B,IAAM,MAAA,mBAAc,CAAA,MAAA;AACpB,MAAA,MAAA,SAAe,EAAU,GAAA,KAAA,CAAA;AAAA,MAC1B,OAAA,OAAA,CAAA,MAAA,IAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAEA,IAAM,MAAA,OAAA,WAAkB,CAAM,MAAA,WAAY,WAAgB,CAAA,CAAA,CAAA;AAE1D,IAAA,MAAM,iBAAsB,CAAA,MAAA,WAAoB,CAAA,KAAA,KAAA,KAAA,CAAA,OAAiB,CAAC,MAAA,GAAA,CAAA,CAAA,CAAA;AAElE,IAAM,MAAA,UAAA,GAAA,eAA8B,KAAA,CAAA,OAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAClC,kBAAU,GAAA,QAAA,CAAA,MAAA;AAAA,MACV,EAAA,CAAG,EAAE,KAAM,CAAA;AAAA,MACX,GAAG,CAAG,CAAA,MAAA,CAAA;AAA4C,MACnD,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,KAAA,CAAA,QAAA,IAAA,OAAA,CAAA,KAAA,CAAA;AAED,KAAM,CAAA,CAAA;AAA8B,IAClC,kBAAU,GAAA,QAAA,CAAA,MAAA;AAAA,MACV,EAAA,CAAG,EAAE,KAAM,CAAA;AAAA,MACX,GAAG,CAAG,CAAA,MAAA,CAAA;AAA2C,MAClD,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,CAAA,KAAA,CAAA,QAAA,IAAA,MAAA,CAAA,KAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,QAAe,GAAA,eAAuB;AACtC,MAAA,kBAA2B,EAAA,OAAA,EAAA,OAAA,EAAA,gBAAA,EAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AAC3B,MAAA,IAAI,aAAa,OAAU,GAAA,KAAA,CAAA;AAE3B,MAAM,IAAA,UAAA,GAAgB,OAAA,GAAW,KAAA,CAAA;AACjC,MAAM,MAAA,MAAA,GAAA,GAAY,GAAK,IAAA,CAAA,EAAU,GAAA,GAAA,CAAA;AACjC,MAAM,MAAA,SAAA,GAAY,IAAK,CAAA,GAAA,CAAI,MAAM,CAAA,CAAA;AACjC,MAAa,MAAA,SAAA,GAAA,IAAA,CAAA,GAAa;AAC1B,MAAa,UAAA,GAAA,UAAA,GAAa,SAAa,GAAA,UAAU,GAAS,SAAA,CAAA;AAE1D,MAAA,UAA6B,GAAA,UAAA,GAAA,SAAA,GAAA,OAAA,GAAA,KAAA,GAAA,SAAA,CAAA;AAAA,MAC3B,MAAA,KAAA;AAAqF,QACrF,SAAA,EAAA,CAAY,uBAAqC,EAAA,GAAA,CAAA,eAAA,EAAA,UAAA,CAAA,IAAA,EAAA,UAAA,CAAA,GAAA,CAAA;AAAA,QACnD,UAAA,EAAA,gBAAA,GAAA,eAAA,GAAA,EAAA;AACA,OAAA,CAAA;AACE,MAAM,IAAA,IAAA,CAAA,KAAA,CAAA,IAAW,UAAkB,CAAA,OAAA,CAAA,IAAA,EAAA;AAAA,QACrC,KAAA,CAAA,QAAA,GAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA;AACA,OAAO;AAAA,MACR,OAAA,KAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAAiB,IACf,MAAA,QAAqB,GAAA,QAAA,CAAA,MAAS,CAAM,EAAA,iBAAc,GAAM,CAAA,CAAA,GAAA,EAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IAC1D,SAAA,IAAA,GAAA;AAEA,MAAA,uBAAgB,EAAA,CAAA;AACd,MAAwB,iBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,iBAAA,EAAA,CAAA;AACxB,MAAoB,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,GAAA,YAAA,CAAA;AACpB,MAAS,IAAA,CAAA,OAAA,CAAA,CAAK;AACd,KAAA;AAAY,IACd,SAAA,qBAAA,GAAA;AAEA,MAAA,MAAA,cAAiC,GAAA,QAAA,CAAA,CAAA,CAAA,KAAA;AAC/B,QAAM,QAAA,CAAA,CAAA,IAAA;AACJ,UAAA,KAAA,UAAgB,CAAA,GAAA;AAAA,iBAEE,CAAA,kBAAA,IAAA,IAAA,EAAA,CAAA;AACd,YAAA,MAAM;AACN,UAAA,KAAA,UAAA,CAAA,KAAA;AAAA,sBAEc,EAAA,CAAA;AACd,YAAW,MAAA;AACX,UAAA,KAAA,UAAA,CAAA,IAAA;AAAA,gBAEc,EAAA,CAAA;AACd,YAAK,MAAA;AACL,UAAA,KAAA,UAAA,CAAA,EAAA;AAAA,yBAEc,CAAA,QAAA,CAAA,CAAA;AACd,YAAA,MAAA;AACA,UAAA,KAAA,UAAA,CAAA,KAAA;AAAA,gBAEc,EAAA,CAAA;AACd,YAAK,MAAA;AACL,UAAA,KAAA,UAAA,CAAA,IAAA;AAAA,yBAEc,CAAA,SAAA,CAAA,CAAA;AACd,YAAA,MAAA;AACA,SAAA;AAAA,OACJ,CAAA,CAAA;AAAA,MACF,MAAC,iBAAA,GAAA,QAAA,CAAA,CAAA,CAAA,KAAA;AACD,QAAM,MAAA,KAAA,GAAA,CAAA,CAAA,MAAA,IAA6B,CAAA,CAAA,MAAC,CAAkB;AACpD,QAAM,aAAA,CAAQ,KAAE,GAAA,CAAA,GAAU,QAAE,GAAA,SAAA,EAAA;AAC5B,UAAc,QAAA,EAAA,KAAA,CAAA,QAAY;AAAsB,UAC9C,gBAAgB,EAAA,KAAA;AAAA,SAAA,CAChB,CAAkB;AAAA,OAAA,CACpB,CAAC;AAAA,MACH,kBAAC,CAAA,GAAA,CAAA,MAAA;AAED,QAAA,gBAAA,CAAmB,QAAU,EAAA,SAAA,EAAA,cAAA,CAAA,CAAA;AAC3B,QAAiB,gBAAA,CAAA,QAAA,EAAU,0BAAyB,CAAA,CAAA;AACpD,OAAiB,CAAA,CAAA;AAAoC,KAAA;AACtD,IACH,SAAA,uBAAA,GAAA;AAEA,MAAA,kBAAmC,CAAA,IAAA,EAAA,CAAA;AACjC,KAAA;AAAwB,IAC1B,SAAA,aAAA,GAAA;AAEA,MAAA,OAAS,CAAgB,KAAA,GAAA,KAAA,CAAA;AACvB,KAAA;AAAgB,IAClB,SAAA,cAAA,CAAA,CAAA,EAAA;AAEA,MAAA,OAAS,eAAe;AACtB,MAAA,CAAA,CAAA,MAAQ,CAAQ,GAAA,GAAA,CAAA,CAAA,gBAAA,CAAA,CAAA;AACf,KAAA;AAAuD,IAC1D,SAAA,eAAA,CAAA,CAAA,EAAA;AAEA,MAAA,IAAA,mBAAyB,MAAe,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,KAAA;AACtC,QAAA;AACA,MAAA,SAAA,CAAU,MAAM,gBAAmB,GAAA,KAAA,CAAA;AAEnC,MAAA,MAAM,EAAE,OAAA,EAAS,OAAQ,EAAA,GAAI,SAAU,CAAA,KAAA,CAAA;AACvC,MAAA,MAAM,SAAS,CAAE,CAAA,KAAA,CAAA;AACjB,MAAA,MAAM,SAAS,CAAE,CAAA,KAAA,CAAA;AAEjB,MAAM,MAAA,WAAA,GAAc,QAAS,CAAA,CAAC,EAAmB,KAAA;AAC/C,QAAA,SAAA,CAAU,KAAQ,GAAA;AAAA,UAChB,GAAG,SAAU,CAAA,KAAA;AAAA,UACb,OAAA,EAAS,OAAU,GAAA,EAAA,CAAG,KAAQ,GAAA,MAAA;AAAA,UAC9B,OAAA,EAAS,OAAU,GAAA,EAAA,CAAG,KAAQ,GAAA,MAAA;AAAA,SAChC,CAAA;AAAA,OACD,CAAA,CAAA;AACD,MAAA,MAAM,eAAkB,GAAA,gBAAA,CAAiB,QAAU,EAAA,WAAA,EAAa,WAAW,CAAA,CAAA;AAC3E,MAAiB,gBAAA,CAAA,QAAA,EAAU,WAAW,MAAM;AAC1C,QAAgB,eAAA,EAAA,CAAA;AAAA,OACjB,CAAA,CAAA;AAED,MAAA,CAAA,CAAE,cAAe,EAAA,CAAA;AAAA,KACnB;AAEA,IAAA,SAAS,KAAQ,GAAA;AACf,MAAA,SAAA,CAAU,KAAQ,GAAA;AAAA,QAChB,KAAO,EAAA,CAAA;AAAA,QACP,GAAK,EAAA,CAAA;AAAA,QACL,OAAS,EAAA,CAAA;AAAA,QACT,OAAS,EAAA,CAAA;AAAA,QACT,gBAAkB,EAAA,KAAA;AAAA,OACpB,CAAA;AAAA,KACF;AAEA,IAAA,SAAS,UAAa,GAAA;AACpB,MAAA,IAAI,QAAQ,KAAO;AAEnB,QAAM,OAAA;AACN,MAAM,MAAA,SAAA,GAAA,MAAoB,CAAA,KAAA,CAAA,CAAA;AAC1B,MAAM,MAAA,UAAA,GAAA,MAAmB,CAAM,MAAA,CAAA,KAAA,CAAA,CAAA;AAC/B,MAAA,MAAM,WAAmB,GAAA,IAAA,CAAA,KAAA,CAAA,IAAU,CAAC;AACpC,MAAM,MAAA,KAAA,GAAA,UAAqB,CAAA,SAAe,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,IAAA,KAAA,WAAA,CAAA,CAAA;AAC1C,MAAA,MAAa,SAAA,GAAA,CAAM,KAAU,GAAA,CAAA,IAAA,SAAU,CAAA,MAAA,CAAA;AACvC,MAAM,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA,SAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAAA,MACR,KAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAM,SAAA,aAAoB,CAAA,KAAA,EAAA;AAC1B,MAAY,MAAA,GAAA,GAAA,KAAA,CAAA,cAAwB,CAAA;AAAA,MACtC,WAAA,CAAA,KAAA,GAAA,CAAA,KAAA,GAAA,GAAA,IAAA,GAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAY,IAAA,GAAA;AACZ,MAAc,IAAA,OAAA,CAAA,KAAA,IAAA,CAAA,cAAqB;AAAA,QACrC,OAAA;AAEA,MAAA,aAAgB,CAAA,WAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACd,KAAA;AACA,IAAc,SAAA,IAAA,GAAA;AAAqB,MACrC,IAAA,MAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,QAAA;AAEA,QAAA,OAAuB;AACrB,MAAA,aAAmB,CAAA,WAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACnB,KAAM;AACN,IAAA,SAAQ,aAAqB,CAAA,MAAA,EAAA,OAAA,GAAA,EAAA,EAAA;AAAqB,MAAA,YACtC,KAAM;AAAA,QAChB,OAAW;AAAA,MAAA,MACO,EAAA,QAAA,EAAA,QAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MAAA,MACf,EAAA,QAAA,EAAA,SAAA,EAAA,gBAAA,EAAA,GAAA;AAAA,QACL,QAAA,EAAA,KAAA,CAAA,QAAA;AACA,QAAA,SAAgB,EAAA,EAAA;AAAA,QACd,gBAAK,EAAA,IAAA;AACH,QAAI,GAAA,OAAA;AACF,OAAU,CAAA;AAAqB,MAAA,QAAA,MAClB;AAAiC,QAC9C,KAAA,SAAA;AAAA,UACF,IAAA,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,EAAA;AACA,YAAA,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,WACG;AACH,UAAI,MAAA;AACF,QAAU,KAAA,QAAA;AAAqB,UAAA,IAAA,SAClB,CAAA,KAAA,CAAA,KAAc,GAAA,QAAA,EAAA;AAAmB,YAC9C,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,SAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,WACF;AACA,UAAA,MAAA;AAAA,QACF,KAAK,WAAA;AACH,UAAA,SAAA,CAAU,MAAM,GAAO,IAAA,SAAA,CAAA;AACvB,UAAK,IAAA,CAAA,QAAA,EAAU,SAAU,CAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAClC,UAAA,MAAA;AAAA,QACF,KAAK,eAAA;AACH,UAAA,SAAA,CAAU,MAAM,GAAO,IAAA,SAAA,CAAA;AACvB,UAAK,IAAA,CAAA,QAAA,EAAU,SAAU,CAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAClC,UAAA,MAAA;AAAA,OACJ;AACA,MAAA,SAAA,CAAU,MAAM,gBAAmB,GAAA,gBAAA,CAAA;AAAA,KACrC;AAEA,IAAA,SAAS,oBAAoB,KAAoB,EAAA;AAC/C,MAAI,IAAA,GAAA,CAAA;AACF,MAAA,IAAA,CAAA,CAAA,GAAqB,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,WAAA,MAAA,SAAA,EAAA;AAAA,QACvB,KAAA,CAAA,cAAA,EAAA,CAAA;AAAA,OACF;AAEA,KAAA;AACE,IAAA,yBAA8B,GAAA;AAC5B,MAAK,IAAA,KAAA,CAAA,kBAAA,EAAA;AAAA,QACP,IAAA,EAAA,CAAA;AAAA,OACF;AAEA,KAAA;AACE,IAAI,SAAG,YAAS,CAAA,CAAA,EAAA;AAEhB,MAAI,IAAA,CAAA,CAAE;AACJ,QAAA,OAAiB;AACjB,MAAO,IAAA,CAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,QACT,CAAA,CAAA,cAAa,EAAA,CAAA;AACX,QAAA,OAAiB,KAAA,CAAA;AACjB,OAAO,MAAA,IAAA,CAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,QACT,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,QACF,OAAA,KAAA,CAAA;AAEA,OAAA;AACE,KAAA;AACE,IAAM,KAAA,CAAA,UAAA,EAAe,MAAA;AACrB,MAAI,eAAiB;AACnB,QAAA,MAAA,IAAQ,GAAQ,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,QAClB,IAAA,EAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UACD,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,SACF;AAED,OAAM,CAAA,CAAA;AACJ,KAAM,CAAA,CAAA;AACN,IAAA,KAAA,CAAA,WAAe,EAAG,CAAA,GAAA,KAAA;AAAA,MACnB,KAAA,EAAA,CAAA;AAED,MAAA,IAAA,CAAA,QAAgB,EAAA,GAAA,CAAA,CAAA;AACd,KAAsB,CAAA,CAAA;AAEtB,IAAoB,SAAA,CAAA,MAAA;AAAwC,MAAA,qBACjD,EAAA,CAAA;AAAA,MACX,iBAAC,GAAA,gBAAA,CAAA,OAAA,EAAA,YAAA,EAAA;AAGD,QAAe,OAAA,EAAA,KAAA;AACf,OAAS,CAAA,CAAA;AAAsB,MAChC,YAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,CAAA;AAED,MAAa,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MAAA,CAAA;AAAA,MAAA,aAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}