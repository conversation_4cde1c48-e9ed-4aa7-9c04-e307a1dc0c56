package com.flower.service;

import com.flower.entity.CartItem;

import java.util.List;

/**
 * 购物车服务接口
 */
public interface CartService {

    /**
     * 添加商品到购物车
     * @param userId 用户ID
     * @param flowerId 花卉ID
     * @param quantity 数量
     * @return 购物车商品
     */
    CartItem addToCart(Long userId, Long flowerId, Integer quantity);

    /**
     * 更新购物车商品数量
     * @param userId 用户ID
     * @param flowerId 花卉ID
     * @param quantity 新数量
     * @return 更新后的购物车商品
     */
    CartItem updateCartItem(Long userId, Long flowerId, Integer quantity);

    /**
     * 从购物车移除商品
     * @param userId 用户ID
     * @param flowerId 花卉ID
     * @return 成功标志
     */
    Boolean removeFromCart(Long userId, Long flowerId);

    /**
     * 获取用户购物车商品列表
     * @param userId 用户ID
     * @return 购物车商品列表
     */
    List<CartItem> getUserCartItems(Long userId);

    /**
     * 清空用户购物车
     * @param userId 用户ID
     * @return 成功标志
     */
    Boolean clearCart(Long userId);

    /**
     * 批量从购物车移除商品
     * @param userId 用户ID
     * @param flowerIds 花卉ID列表
     * @return 成功删除的数量
     */
    Integer batchRemoveFromCart(Long userId, List<Long> flowerIds);

    /**
     * 获取购物车商品数量
     * @param userId 用户ID
     * @return 商品数量
     */
    Integer getCartItemCount(Long userId);
}
