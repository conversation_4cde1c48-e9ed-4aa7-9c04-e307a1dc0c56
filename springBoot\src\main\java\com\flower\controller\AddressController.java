package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.UserAddress;
import com.flower.service.AddressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 收货地址控制器
 */
@Slf4j
@RestController
@RequestMapping("/address")
@CrossOrigin(origins = "*")
public class AddressController {

    @Autowired
    private AddressService addressService;

    /**
     * 获取用户所有收货地址
     */
    @GetMapping("/list/{userId}")
    public Result<List<UserAddress>> getUserAddresses(@PathVariable Long userId) {
        try {
            List<UserAddress> addresses = addressService.getUserAddresses(userId);
            return Result.success(addresses);
        } catch (Exception e) {
            log.error("获取收货地址列表失败", e);
            return Result.error("获取收货地址列表失败");
        }
    }

    /**
     * 根据ID获取地址详情
     */
    @GetMapping("/detail/{id}")
    public Result<UserAddress> getAddressById(@PathVariable Long id) {
        try {
            UserAddress address = addressService.getAddressById(id);
            if (address == null) {
                return Result.notFound("地址不存在");
            }
            return Result.success(address);
        } catch (Exception e) {
            log.error("获取地址详情失败", e);
            return Result.error("获取地址详情失败");
        }
    }

    /**
     * 添加收货地址
     */
    @PostMapping("/add")
    public Result<UserAddress> addAddress(@RequestBody UserAddress address) {
        try {
            // 基本验证
            if (address.getUserId() == null || 
                address.getRecipientName() == null || address.getRecipientName().trim().isEmpty() ||
                address.getRecipientPhone() == null || address.getRecipientPhone().trim().isEmpty() ||
                address.getProvince() == null || address.getProvince().trim().isEmpty() ||
                address.getCity() == null || address.getCity().trim().isEmpty() ||
                address.getDistrict() == null || address.getDistrict().trim().isEmpty() ||
                address.getDetailedAddress() == null || address.getDetailedAddress().trim().isEmpty()) {
                return Result.paramError("地址信息不完整");
            }

            UserAddress savedAddress = addressService.addAddress(address);
            return Result.success("添加地址成功", savedAddress);
        } catch (Exception e) {
            log.error("添加收货地址失败", e);
            return Result.error("添加收货地址失败");
        }
    }

    /**
     * 更新收货地址
     */
    @PutMapping("/update")
    public Result<UserAddress> updateAddress(@RequestBody UserAddress address) {
        try {
            if (address.getId() == null) {
                return Result.paramError("地址ID不能为空");
            }

            UserAddress updatedAddress = addressService.updateAddress(address);
            return Result.success("更新地址成功", updatedAddress);
        } catch (Exception e) {
            log.error("更新收货地址失败", e);
            return Result.error("更新收货地址失败");
        }
    }

    /**
     * 删除收货地址
     */
    @DeleteMapping("/delete")
    public Result<Boolean> deleteAddress(@RequestBody Map<String, Object> params) {
        try {
            Long id = Long.valueOf(params.get("id").toString());
            Long userId = Long.valueOf(params.get("userId").toString());

            Boolean success = addressService.deleteAddress(id, userId);
            if (success) {
                return Result.success("删除地址成功", true);
            } else {
                return Result.error("删除地址失败，地址不存在或无权限");
            }
        } catch (Exception e) {
            log.error("删除收货地址失败", e);
            return Result.error("删除收货地址失败");
        }
    }

    /**
     * 设置默认地址
     */
    @PostMapping("/setDefault")
    public Result<Boolean> setDefaultAddress(@RequestBody Map<String, Object> params) {
        try {
            Long id = Long.valueOf(params.get("id").toString());
            Long userId = Long.valueOf(params.get("userId").toString());

            Boolean success = addressService.setDefaultAddress(id, userId);
            if (success) {
                return Result.success("设置默认地址成功", true);
            } else {
                return Result.error("设置默认地址失败，地址不存在或无权限");
            }
        } catch (Exception e) {
            log.error("设置默认地址失败", e);
            return Result.error("设置默认地址失败");
        }
    }

    /**
     * 获取用户默认地址
     */
    @GetMapping("/default/{userId}")
    public Result<UserAddress> getDefaultAddress(@PathVariable Long userId) {
        try {
            UserAddress address = addressService.getDefaultAddress(userId);
            return Result.success(address);
        } catch (Exception e) {
            log.error("获取默认地址失败", e);
            return Result.error("获取默认地址失败");
        }
    }
}
