package com.flower.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.io.File;

/**
 * Web配置类
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取项目根目录
        String projectPath = System.getProperty("user.dir");
        String imagePath = "file:" + projectPath + "/springBoot/src/main/resources/image/user-image/";

        System.out.println("配置静态资源路径: " + imagePath);

        // 配置用户头像静态资源访问 - 使用绝对路径
        registry.addResourceHandler("/image/user-image/**")
                .addResourceLocations(imagePath);

        // 配置商品图片静态资源访问
        registry.addResourceHandler("/image/shop-image/**")
                .addResourceLocations("file:" + projectPath + "/springBoot/src/main/resources/image/shop-image/");

        // 配置旧的商品图片路径格式（兼容数据库中的旧路径）
        registry.addResourceHandler("/image/flower/**")
                .addResourceLocations("file:" + projectPath + "/springBoot/src/main/resources/image/shop-image/")
                .addResourceLocations("classpath:/image/shop-image/");

        // 添加classpath路径作为备选
        registry.addResourceHandler("/image/user-image/**")
                .addResourceLocations("classpath:/image/user-image/");
        registry.addResourceHandler("/image/shop-image/**")
                .addResourceLocations("classpath:/image/shop-image/");
    }
}
