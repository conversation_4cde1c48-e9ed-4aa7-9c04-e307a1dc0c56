{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/dropdown/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\nimport Dropdown from './src/dropdown.vue'\nimport DropdownItem from './src/dropdown-item.vue'\nimport DropdownMenu from './src/dropdown-menu.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElDropdown: SFCWithInstall<typeof Dropdown> & {\n  DropdownItem: typeof DropdownItem\n  DropdownMenu: typeof DropdownMenu\n} = withInstall(Dropdown, {\n  DropdownItem,\n  DropdownMenu,\n})\nexport default ElDropdown\nexport const ElDropdownItem: SFCWithInstall<typeof DropdownItem> =\n  withNoopInstall(DropdownItem)\nexport const ElDropdownMenu: SFCWithInstall<typeof DropdownMenu> =\n  withNoopInstall(DropdownMenu)\nexport * from './src/dropdown'\nexport * from './src/instance'\nexport * from './src/tokens'\n"], "names": [], "mappings": ";;;;;;;AAIY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,YAAY;AACd,EAAE,YAAY;AACd,CAAC,EAAE;AAES,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY,EAAE;AAChD,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY;;;;"}