package com.flower.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户自取信息实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_pickup_info")
public class UserPickupInfo {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 取货人姓名
     */
    private String pickupName;

    /**
     * 取货人电话
     */
    private String pickupPhone;

    /**
     * 取货时间
     */
    private String pickupTime;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 备用收货地址（用于突发情况外卖派送）
     */
    private String backupAddress;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
