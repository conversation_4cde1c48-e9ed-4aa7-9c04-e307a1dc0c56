{"version": 3, "file": "dropdown.mjs", "sources": ["../../../../../../packages/components/dropdown/src/dropdown.ts"], "sourcesContent": ["import { buildProps, definePropType, iconPropType } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { createCollectionWithScope } from '@element-plus/components/collection'\nimport {\n  useTooltipContentProps,\n  useTooltipTriggerProps,\n} from '@element-plus/components/tooltip'\nimport { type Placement, roleTypes } from '@element-plus/components/popper'\n\nimport type { Options } from '@popperjs/core'\nimport type { ButtonProps, ButtonType } from '@element-plus/components/button'\nimport type { ComponentInternalInstance, ComputedRef } from 'vue'\nimport type { Nullable } from '@element-plus/utils'\n\nexport interface IElDropdownInstance {\n  instance?: ComponentInternalInstance\n  dropdownSize?: ComputedRef<string>\n  handleClick?: () => void\n  commandHandler?: (...arg: any[]) => void\n  show?: () => void\n  hide?: () => void\n  trigger?: ComputedRef<string>\n  hideOnClick?: ComputedRef<boolean>\n  triggerElm?: ComputedRef<Nullable<HTMLButtonElement>>\n}\n\nexport const dropdownProps = buildProps({\n  /**\n   * @description how to trigger\n   */\n  trigger: useTooltipTriggerProps.trigger,\n  triggerKeys: {\n    type: definePropType<string[]>(Array),\n    default: () => [\n      EVENT_CODE.enter,\n      EVENT_CODE.numpadEnter,\n      EVENT_CODE.space,\n      EVENT_CODE.down,\n    ],\n  },\n  effect: {\n    ...useTooltipContentProps.effect,\n    default: 'light',\n  },\n  /**\n   * @description menu button type, refer to `Button` Component, only works when `split-button` is true\n   */\n  type: {\n    type: definePropType<ButtonType>(String),\n  },\n  /**\n   * @description placement of pop menu\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    default: 'bottom',\n  },\n  /**\n   * @description [popper.js](https://popper.js.org/docs/v2/) parameters\n   */\n  popperOptions: {\n    type: definePropType<Partial<Options>>(Object),\n    default: () => ({}),\n  },\n  id: String,\n  /**\n   * @description menu size, also works on the split button\n   */\n  size: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether a button group is displayed\n   */\n  splitButton: Boolean,\n  /**\n   * @description whether to hide menu after clicking menu-item\n   */\n  hideOnClick: {\n    type: Boolean,\n    default: true,\n  },\n  loop: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description delay time before show a dropdown (only works when trigger is `hover`)\n   */\n  showTimeout: {\n    type: Number,\n    default: 150,\n  },\n  /**\n   * @description delay time before hide a dropdown (only works when trigger is `hover`)\n   */\n  hideTimeout: {\n    type: Number,\n    default: 150,\n  },\n  /**\n   * @description [tabindex](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/tabindex) of Dropdown\n   */\n  tabindex: {\n    type: definePropType<number | string>([Number, String]),\n    default: 0,\n  },\n  /**\n   * @description the max height of menu\n   */\n  maxHeight: {\n    type: definePropType<number | string>([Number, String]),\n    default: '',\n  },\n  /**\n   * @description custom class name for Dropdown's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether to disable\n   */\n  disabled: Boolean,\n  /**\n   * @description the ARIA role attribute for the dropdown menu. Depending on the use case, you may want to change this to 'navigation'\n   */\n  role: {\n    type: String,\n    values: roleTypes,\n    default: 'menu',\n  },\n  buttonProps: {\n    type: definePropType<Partial<ButtonProps>>(Object),\n  },\n  /**\n   * @description whether the dropdown popup is teleported to the body\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description when dropdown inactive and `persistent` is `false` , dropdown menu will be destroyed\n   */\n  persistent: {\n    type: Boolean,\n    default: true,\n  },\n} as const)\n\nexport const dropdownItemProps = buildProps({\n  /**\n   * @description a command to be dispatched to Dropdown's `command` callback\n   */\n  command: {\n    type: [Object, String, Number],\n    default: () => ({}),\n  },\n  /**\n   * @description whether the item is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether a divider is displayed\n   */\n  divided: Boolean,\n  textValue: String,\n  /**\n   * @description custom icon\n   */\n  icon: {\n    type: iconPropType,\n  },\n} as const)\n\nexport const dropdownMenuProps = buildProps({\n  onKeydown: { type: definePropType<(e: KeyboardEvent) => void>(Function) },\n})\n\nexport const FIRST_KEYS = [\n  EVENT_CODE.down,\n  EVENT_CODE.pageDown,\n  EVENT_CODE.home,\n]\n\nexport const LAST_KEYS = [EVENT_CODE.up, EVENT_CODE.pageUp, EVENT_CODE.end]\n\nexport const FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS]\n\nconst {\n  ElCollection,\n  ElCollectionItem,\n  COLLECTION_INJECTION_KEY,\n  COLLECTION_ITEM_INJECTION_KEY,\n} = createCollectionWithScope('Dropdown')\n\nexport {\n  ElCollection,\n  ElCollectionItem,\n  COLLECTION_INJECTION_KEY as DROPDOWN_COLLECTION_INJECTION_KEY,\n  COLLECTION_ITEM_INJECTION_KEY as DROPDOWN_COLLECTION_ITEM_INJECTION_KEY,\n}\n"], "names": [], "mappings": ";;;;;;;;AAQY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,OAAO,EAAE,sBAAsB,CAAC,OAAO;AACzC,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,UAAU,CAAC,KAAK;AACtB,MAAM,UAAU,CAAC,WAAW;AAC5B,MAAM,UAAU,CAAC,KAAK;AACtB,MAAM,UAAU,CAAC,IAAI;AACrB,KAAK;AACL,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,GAAG,sBAAsB,CAAC,MAAM;AACpC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,QAAQ;AACrB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE,OAAO;AACtB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,SAAS;AACrB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,UAAU,EAAE,sBAAsB,CAAC,UAAU;AAC/C,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;AAClC,IAAI,OAAO,EAAE,OAAO,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE;AAC/C,CAAC,EAAE;AACS,MAAC,UAAU,GAAG;AAC1B,EAAE,UAAU,CAAC,IAAI;AACjB,EAAE,UAAU,CAAC,QAAQ;AACrB,EAAE,UAAU,CAAC,IAAI;AACjB,EAAE;AACU,MAAC,SAAS,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE;AAChE,MAAC,eAAe,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,SAAS,EAAE;AACxD,MAAC;AACN,EAAE,YAAY;AACd,EAAE,gBAAgB;AAClB,EAAE,wBAAwB;AAC1B,EAAE,6BAA6B;AAC/B,CAAC,GAAG,yBAAyB,CAAC,UAAU;;;;"}