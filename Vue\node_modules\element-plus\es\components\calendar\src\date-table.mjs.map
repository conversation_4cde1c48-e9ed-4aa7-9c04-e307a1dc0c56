{"version": 3, "file": "date-table.mjs", "sources": ["../../../../../../packages/components/calendar/src/date-table.ts"], "sourcesContent": ["import { buildProps, definePropType, isObject } from '@element-plus/utils'\nimport { rangeArr } from '@element-plus/components/time-picker'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport type CalendarDateCellType = 'next' | 'prev' | 'current'\nexport type CalendarDateCell = {\n  text: number\n  type: CalendarDateCellType\n}\n\nexport const getPrevMonthLastDays = (date: Dayjs, count: number) => {\n  const lastDay = date.subtract(1, 'month').endOf('month').date()\n  return rangeArr(count).map((_, index) => lastDay - (count - index - 1))\n}\n\nexport const getMonthDays = (date: Dayjs) => {\n  const days = date.daysInMonth()\n  return rangeArr(days).map((_, index) => index + 1)\n}\n\nexport const toNestedArr = (days: CalendarDateCell[]) =>\n  rangeArr(days.length / 7).map((index) => {\n    const start = index * 7\n    return days.slice(start, start + 7)\n  })\n\nexport const dateTableProps = buildProps({\n  selectedDay: {\n    type: definePropType<Dayjs>(Object),\n  },\n  range: {\n    type: definePropType<[Dayjs, Dayjs]>(Array),\n  },\n  date: {\n    type: definePropType<Dayjs>(Object),\n    required: true,\n  },\n  hideHeader: {\n    type: Boolean,\n  },\n} as const)\nexport type DateTableProps = ExtractPropTypes<typeof dateTableProps>\nexport type DateTablePropsPublic = __ExtractPublicPropTypes<\n  typeof dateTableProps\n>\n\nexport const dateTableEmits = {\n  pick: (value: Dayjs) => isObject(value),\n}\nexport type DateTableEmits = typeof dateTableEmits\n"], "names": [], "mappings": ";;;;AAEY,MAAC,oBAAoB,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK;AACrD,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;AAClE,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,OAAO,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC1E,EAAE;AACU,MAAC,YAAY,GAAG,CAAC,IAAI,KAAK;AACtC,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAClC,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;AACrD,EAAE;AACU,MAAC,WAAW,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAC9E,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AAC1B,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACtC,CAAC,EAAE;AACS,MAAC,cAAc,GAAG,UAAU,CAAC;AACzC,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,cAAc,GAAG;AAC9B,EAAE,IAAI,EAAE,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC;AAClC;;;;"}