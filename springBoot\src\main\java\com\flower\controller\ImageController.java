package com.flower.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 图片访问控制器
 */
@Slf4j
@RestController
@RequestMapping("/image")
@CrossOrigin(origins = "*")
public class ImageController {

    /**
     * 获取用户头像
     */
    @GetMapping("/user-image/{filename}")
    public ResponseEntity<Resource> getUserImage(@PathVariable String filename) {
        try {
            log.info("请求用户头像: {}", filename);

            // 首先尝试从classpath加载
            ClassPathResource classPathResource = new ClassPathResource("image/user-image/" + filename);
            if (classPathResource.exists()) {
                log.info("从classpath加载头像: {}", filename);
                return createImageResponse(classPathResource, filename);
            }

            // 如果classpath中没有，尝试从文件系统加载
            String projectPath = System.getProperty("user.dir");
            Path imagePath = Paths.get(projectPath, "springBoot", "src", "main", "resources", "image", "user-image", filename);

            log.info("尝试从文件系统加载头像: {}", imagePath.toString());

            if (Files.exists(imagePath)) {
                FileSystemResource fileResource = new FileSystemResource(imagePath.toFile());
                log.info("从文件系统加载头像成功: {}", filename);
                return createImageResponse(fileResource, filename);
            }

            // 如果都找不到，返回404
            log.warn("头像文件不存在: {}", filename);
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error("获取用户头像失败: {}", filename, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取商品图片
     */
    @GetMapping("/shop-image/{filename}")
    public ResponseEntity<Resource> getShopImage(@PathVariable String filename) {
        try {
            log.info("请求商品图片: {}", filename);
            
            // 首先尝试从classpath加载
            ClassPathResource classPathResource = new ClassPathResource("image/shop-image/" + filename);
            if (classPathResource.exists()) {
                log.info("从classpath加载商品图片: {}", filename);
                return createImageResponse(classPathResource, filename);
            }
            
            // 如果classpath中没有，尝试从文件系统加载
            String projectPath = System.getProperty("user.dir");
            Path imagePath = Paths.get(projectPath, "springBoot", "src", "main", "resources", "image", "shop-image", filename);
            
            log.info("尝试从文件系统加载商品图片: {}", imagePath.toString());
            
            if (Files.exists(imagePath)) {
                FileSystemResource fileResource = new FileSystemResource(imagePath.toFile());
                log.info("从文件系统加载商品图片成功: {}", filename);
                return createImageResponse(fileResource, filename);
            }
            
            log.warn("商品图片文件不存在: {}", filename);
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            log.error("获取商品图片失败: {}", filename, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 创建图片响应
     */
    private ResponseEntity<Resource> createImageResponse(Resource resource, String filename) throws IOException {
        // 根据文件扩展名确定Content-Type
        String contentType = getContentType(filename);
        
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + filename + "\"")
                .body(resource);
    }

    /**
     * 根据文件名获取Content-Type
     */
    private String getContentType(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "webp":
                return "image/webp";
            default:
                return "application/octet-stream";
        }
    }

    /**
     * 测试接口 - 检查图片目录
     */
    @GetMapping("/test")
    public ResponseEntity<String> testImageAccess() {
        try {
            String projectPath = System.getProperty("user.dir");
            Path userImagePath = Paths.get(projectPath, "springBoot", "src", "main", "resources", "image", "user-image");
            
            StringBuilder result = new StringBuilder();
            result.append("项目路径: ").append(projectPath).append("\n");
            result.append("用户头像目录: ").append(userImagePath.toString()).append("\n");
            result.append("目录是否存在: ").append(Files.exists(userImagePath)).append("\n");
            
            if (Files.exists(userImagePath)) {
                result.append("目录中的文件:\n");
                Files.list(userImagePath).forEach(file -> {
                    result.append("  - ").append(file.getFileName()).append("\n");
                });
            }
            
            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            log.error("测试图片访问失败", e);
            return ResponseEntity.internalServerError().body("测试失败: " + e.getMessage());
        }
    }
}
