import type { CSSProperties, ComputedRef, InjectionKey, Ref } from 'vue';
import type { UseNamespaceReturn } from 'element-plus/es/hooks';
export type DialogContext = {
    dialogRef: Ref<HTMLElement | undefined>;
    headerRef: Ref<HTMLElement | undefined>;
    bodyId: Ref<string>;
    ns: UseNamespaceReturn;
    rendered: Ref<boolean>;
    style: ComputedRef<CSSProperties>;
};
export declare const dialogInjectionKey: InjectionKey<DialogContext>;
