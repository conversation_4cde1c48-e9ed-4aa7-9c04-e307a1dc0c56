server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: flower-shop

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: 123456

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# 微信小程序配置
wechat:
  miniprogram:
    # 开发环境使用模拟登录，生产环境请替换为真实的AppID和AppSecret
    app-id: your_app_id_here
    app-secret: your_app_secret_here

# JWT配置
jwt:
  secret: flower-admin-secret-key-2025
  expiration: 86400  # 24小时

# 应用配置
app:
  # 服务器配置
  server:
    # 开发环境服务器地址，生产环境请修改为实际域名
    base-url: http://localhost:8080
    # 图片访问路径前缀
    image-url-prefix: ${app.server.base-url}/api
    
# Logging Configuration
logging:
  level:
    com.flower: debug
    org.springframework.web: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
