package com.flower.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信小程序配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wechat.miniprogram")
public class WeChatConfig {
    
    /**
     * 小程序AppID
     */
    private String appId;
    
    /**
     * 小程序AppSecret
     */
    private String appSecret;
    
    /**
     * 检查配置是否有效
     */
    public boolean isConfigValid() {
        return appId != null && !appId.equals("your_app_id_here") 
            && appSecret != null && !appSecret.equals("your_app_secret_here");
    }
}
