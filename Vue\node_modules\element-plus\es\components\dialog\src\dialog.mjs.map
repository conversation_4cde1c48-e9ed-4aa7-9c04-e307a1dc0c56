{"version": 3, "file": "dialog.mjs", "sources": ["../../../../../../packages/components/dialog/src/dialog.ts"], "sourcesContent": ["import { buildProps, definePropType, isBoolean } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { teleportProps } from '@element-plus/components/teleport'\nimport { dialogContentProps } from './dialog-content'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Dialog from './dialog.vue'\n\ntype DoneFn = (cancel?: boolean) => void\nexport type DialogBeforeCloseFn = (done: DoneFn) => void\n\nexport const dialogProps = buildProps({\n  ...dialogContentProps,\n  /**\n   * @description whether to append Dialog itself to body. A nested Dialog should have this attribute set to `true`\n   */\n  appendToBody: Boolean,\n  /**\n   * @description which element the Dialog appends to\n   */\n  appendTo: {\n    type: teleportProps.to.type,\n    default: 'body',\n  },\n  /**\n   * @description callback before Dialog closes, and it will prevent <PERSON><PERSON> from closing, use done to close the dialog\n   */\n  beforeClose: {\n    type: definePropType<DialogBeforeCloseFn>(Function),\n  },\n  /**\n   * @description destroy elements in Dialog when closed\n   */\n  destroyOnClose: Boolean,\n  /**\n   * @description whether the Dialog can be closed by clicking the mask\n   */\n  closeOnClickModal: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether the Dialog can be closed by pressing ESC\n   */\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether scroll of body is disabled while Dialog is displayed\n   */\n  lockScroll: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether a mask is displayed\n   */\n  modal: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description the Time(milliseconds) before open\n   */\n  openDelay: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description the Time(milliseconds) before close\n   */\n  closeDelay: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description value for `margin-top` of Dialog CSS, default is 15vh\n   */\n  top: {\n    type: String,\n  },\n  /**\n   * @description visibility of Dialog\n   */\n  modelValue: Boolean,\n  /**\n   * @description custom class names for mask\n   */\n  modalClass: String,\n  /**\n   * @description custom class names for header wrapper\n   */\n  headerClass: String,\n  /**\n   * @description custom class names for body wrapper\n   */\n  bodyClass: String,\n  /**\n   * @description custom class names for footer wrapper\n   */\n  footerClass: String,\n  /**\n   * @description width of Dialog, default is 50%\n   */\n  width: {\n    type: [String, Number],\n  },\n  /**\n   * @description same as z-index in native CSS, z-order of dialog\n   */\n  zIndex: {\n    type: Number,\n  },\n  trapFocus: Boolean,\n  /**\n   * @description header's aria-level attribute\n   */\n  headerAriaLevel: {\n    type: String,\n    default: '2',\n  },\n} as const)\n\nexport type DialogProps = ExtractPropTypes<typeof dialogProps>\nexport type DialogPropsPublic = __ExtractPublicPropTypes<typeof dialogProps>\n\nexport const dialogEmits = {\n  open: () => true,\n  opened: () => true,\n  close: () => true,\n  closed: () => true,\n  [UPDATE_MODEL_EVENT]: (value: boolean) => isBoolean(value),\n  openAutoFocus: () => true,\n  closeAutoFocus: () => true,\n}\nexport type DialogEmits = typeof dialogEmits\nexport type DialogInstance = InstanceType<typeof Dialog> & unknown\n"], "names": [], "mappings": ";;;;;;AAIY,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,GAAG,kBAAkB;AACvB,EAAE,YAAY,EAAE,OAAO;AACvB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,aAAa,CAAC,EAAE,CAAC,IAAI;AAC/B,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,cAAc,EAAE,OAAO;AACzB,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;AAC3B,EAAE,IAAI,EAAE,MAAM,IAAI;AAClB,EAAE,MAAM,EAAE,MAAM,IAAI;AACpB,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,MAAM,EAAE,MAAM,IAAI;AACpB,EAAE,CAAC,kBAAkB,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;AACnD,EAAE,aAAa,EAAE,MAAM,IAAI;AAC3B,EAAE,cAAc,EAAE,MAAM,IAAI;AAC5B;;;;"}