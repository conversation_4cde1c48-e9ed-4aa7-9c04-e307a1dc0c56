package com.flower.service;

import com.flower.entity.User;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 微信登录
     * @param code 微信登录凭证
     * @return 用户信息
     */
    User wechatLogin(String code);

    /**
     * 根据openid获取用户
     * @param openid 微信openid
     * @return 用户信息
     */
    User getUserByOpenid(String openid);

    /**
     * 更新用户信息
     * @param user 用户信息
     * @return 更新后的用户
     */
    User updateUser(User user);

    /**
     * 更新用户手机号
     * @param userId 用户ID
     * @param phone 手机号码
     * @return 更新后的用户
     */
    User updateUserPhone(Long userId, String phone);

    /**
     * 根据ID获取用户
     * @param userId 用户ID
     * @return 用户信息
     */
    User findById(Long userId);

    /**
     * 保存用户
     * @param user 用户信息
     * @return 保存后的用户
     */
    User save(User user);

    /**
     * 上传用户头像
     * @param file 头像文件
     * @param userId 用户ID
     * @return 头像URL
     */
    String uploadAvatar(MultipartFile file, Long userId);

    /**
     * 更新用户头像、昵称和手机号（登录时使用）
     * @param userId 用户ID
     * @param nickname 昵称
     * @param avatarBase64 头像Base64数据
     * @param phone 手机号码
     * @return 更新后的用户
     */
    User updateUserProfile(Long userId, String nickname, String avatarBase64, String phone);
}
