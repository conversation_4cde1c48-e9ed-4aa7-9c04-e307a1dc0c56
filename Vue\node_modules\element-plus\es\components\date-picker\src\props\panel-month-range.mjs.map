{"version": 3, "file": "panel-month-range.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-month-range.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { panelRangeSharedProps } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const panelMonthRangeProps = buildProps({\n  ...panelRangeSharedProps,\n} as const)\n\nexport const panelMonthRangeEmits = [\n  'pick',\n  'set-picker-option',\n  'calendar-change',\n]\n\nexport type PanelMonthRangeProps = ExtractPropTypes<typeof panelMonthRangeProps>\nexport type PanelMonthRangePropsPublic = __ExtractPublicPropTypes<\n  typeof panelMonthRangeProps\n>\n"], "names": [], "mappings": ";;;AAEY,MAAC,oBAAoB,GAAG,UAAU,CAAC;AAC/C,EAAE,GAAG,qBAAqB;AAC1B,CAAC,EAAE;AACS,MAAC,oBAAoB,GAAG;AACpC,EAAE,MAAM;AACR,EAAE,mBAAmB;AACrB,EAAE,iBAAiB;AACnB;;;;"}