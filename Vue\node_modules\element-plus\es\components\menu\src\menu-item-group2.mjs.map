{"version": 3, "file": "menu-item-group2.mjs", "sources": ["../../../../../../packages/components/menu/src/menu-item-group.vue"], "sourcesContent": ["<template>\n  <li :class=\"ns.b()\">\n    <div :class=\"ns.e('title')\">\n      <template v-if=\"!$slots.title\">{{ title }}</template>\n      <slot v-else name=\"title\" />\n    </div>\n    <ul>\n      <slot />\n    </ul>\n  </li>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport { menuItemGroupProps } from './menu-item-group'\n\ndefineOptions({\n  name: 'ElMenuItemGroup',\n})\ndefineProps(menuItemGroupProps)\nconst ns = useNamespace('menu-item-group')\n</script>\n"], "names": [], "mappings": ";;;;;mCAgBc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AACR,CAAA,CAAA,CAAA;;;;;AAEA,IAAM,MAAA,EAAA,GAAK,aAAa,iBAAiB,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;"}