import ElFocusTrap from './src/focus-trap.mjs';
export { default as ElFocusTrap, default } from './src/focus-trap.mjs';
export { FOCUSOUT_PREVENTED, FOCUSOUT_PREVENTED_OPTS, FOCUS_AFTER_RELEASED, FOCUS_AFTER_TRAPPED, FOCUS_AFTER_TRAPPED_OPTS, FOCUS_TRAP_INJECTION_KEY, ON_RELEASE_FOCUS_EVT, ON_TRAP_FOCUS_EVT } from './src/tokens.mjs';
export { createFocusOutPreventedEvent, focusFirstDescendant, focusableStack, getEdges, getVisibleElement, isFocusCausedByUserEvent, isHidden, obtainAllFocusableElements, tryFocus, useFocusReason } from './src/utils.mjs';
//# sourceMappingURL=index.mjs.map
