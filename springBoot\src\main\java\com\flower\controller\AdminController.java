package com.flower.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.common.Result;
import com.flower.entity.*;
import com.flower.mapper.*;
import com.flower.service.AdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 管理员控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@CrossOrigin(origins = "*")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    @Autowired
    private FlowerReviewMapper flowerReviewMapper;

    @Autowired
    private RegionMapper regionMapper;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> params, HttpServletRequest request) {
        try {
            String username = params.get("username");
            String password = params.get("password");
            
            if (username == null || username.trim().isEmpty()) {
                return Result.paramError("用户名不能为空");
            }
            
            if (password == null || password.trim().isEmpty()) {
                return Result.paramError("密码不能为空");
            }
            
            // 登录验证
            AdminUser adminUser = adminService.login(username, password);
            
            // 生成token
            String token = adminService.generateToken(adminUser);
            
            // 更新最后登录信息
            String ipAddress = getClientIpAddress(request);
            adminService.updateLastLogin(adminUser.getId(), ipAddress);
            
            // 记录登录日志
            adminService.logAction(adminUser.getId(), adminUser.getUsername(), "LOGIN", 
                                 "ADMIN", adminUser.getId().toString(), "管理员登录", 
                                 ipAddress, request.getHeader("User-Agent"));
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", adminUser);
            
            return Result.success("登录成功", result);
        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 验证token
     */
    @GetMapping("/verify")
    public Result<AdminUser> verifyToken(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("Token不能为空");
            }
            
            AdminUser adminUser = adminService.verifyToken(token);
            return Result.success("Token验证成功", adminUser);
        } catch (Exception e) {
            log.error("Token验证失败", e);
            return Result.error("Token验证失败: " + e.getMessage());
        }
    }

    /**
     * 管理员登出
     */
    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token != null) {
                AdminUser adminUser = adminService.verifyToken(token);
                
                // 记录登出日志
                String ipAddress = getClientIpAddress(request);
                adminService.logAction(adminUser.getId(), adminUser.getUsername(), "LOGOUT", 
                                     "ADMIN", adminUser.getId().toString(), "管理员登出", 
                                     ipAddress, request.getHeader("User-Agent"));
            }
            
            return Result.success("登出成功");
        } catch (Exception e) {
            log.error("管理员登出失败", e);
            return Result.success("登出成功"); // 即使出错也返回成功，因为前端会清除token
        }
    }

    /**
     * 获取统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 用户总数
            long userCount = userMapper.selectCount(null);
            stats.put("userCount", userCount);
            
            // 商品总数
            long flowerCount = flowerMapper.selectCount(null);
            stats.put("flowerCount", flowerCount);
            
            // 订单总数
            long orderCount = orderMapper.selectCount(null);
            stats.put("orderCount", orderCount);
            
            // 总销售额（这里需要根据实际订单表结构计算）
            // 假设订单表有totalAmount字段
            BigDecimal totalSales = BigDecimal.ZERO;
            List<Order> orders = orderMapper.selectList(null);
            for (Order order : orders) {
                if (order.getTotalAmount() != null) {
                    totalSales = totalSales.add(order.getTotalAmount());
                }
            }
            stats.put("totalSales", totalSales);
            
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取统计数据失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取图表数据
     */
    @GetMapping("/chart/{type}")
    public Result<Map<String, Object>> getChartData(@PathVariable String type) {
        try {
            Map<String, Object> chartData = new HashMap<>();
            
            switch (type) {
                case "order-week":
                case "order-month":
                    // 订单趋势数据
                    int days = type.equals("order-week") ? 7 : 30;
                    List<String> dates = new ArrayList<>();
                    List<Integer> counts = new ArrayList<>();
                    
                    LocalDateTime endDate = LocalDateTime.now();
                    LocalDateTime startDate = endDate.minusDays(days - 1);
                    
                    for (int i = 0; i < days; i++) {
                        LocalDateTime currentDate = startDate.plusDays(i);
                        String dateStr = currentDate.format(DateTimeFormatter.ofPattern("MM-dd"));
                        dates.add(dateStr);
                        
                        // 查询当天订单数量
                        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
                        wrapper.ge(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay());
                        wrapper.lt(Order::getCreatedAt, currentDate.toLocalDate().atStartOfDay().plusDays(1));
                        long dayCount = orderMapper.selectCount(wrapper);
                        counts.add((int) dayCount);
                    }
                    
                    chartData.put("dates", dates);
                    chartData.put("counts", counts);
                    break;
                    
                case "category-sales":
                    // 分类销量数据
                    List<Category> categories = categoryMapper.selectList(null);
                    List<Map<String, Object>> categoryData = new ArrayList<>();
                    
                    for (Category category : categories) {
                        // 统计该分类下商品的销量
                        LambdaQueryWrapper<Flower> flowerWrapper = new LambdaQueryWrapper<>();
                        flowerWrapper.eq(Flower::getCategoryId, category.getId());
                        List<Flower> flowers = flowerMapper.selectList(flowerWrapper);
                        
                        int totalSales = 0;
                        for (Flower flower : flowers) {
                            if (flower.getSalesCount() != null) {
                                totalSales += flower.getSalesCount();
                            }
                        }
                        
                        if (totalSales > 0) {
                            Map<String, Object> item = new HashMap<>();
                            item.put("name", category.getName());
                            item.put("value", totalSales);
                            categoryData.add(item);
                        }
                    }
                    
                    chartData.put("categories", categoryData);
                    break;
                    
                default:
                    return Result.error("不支持的图表类型");
            }
            
            return Result.success(chartData);
        } catch (Exception e) {
            log.error("获取图表数据失败", e);
            return Result.error("获取图表数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public Result<PageResult<User>> getUsers(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword) {
        try {
            Page<User> page = new Page<>(current, size);
            LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
            
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like(User::getNickname, keyword)
                       .or()
                       .like(User::getPhone, keyword);
            }
            
            wrapper.orderByDesc(User::getCreatedAt);
            
            Page<User> result = userMapper.selectPage(page, wrapper);
            
            PageResult<User> pageResult = new PageResult<>();
            pageResult.setRecords(result.getRecords());
            pageResult.setTotal(result.getTotal());
            pageResult.setCurrent(result.getCurrent());
            pageResult.setSize(result.getSize());
            pageResult.setPages(result.getPages());
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取订单列表
     */
    @GetMapping("/orders")
    public Result<PageResult<Order>> getOrders(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(defaultValue = "created_at") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        try {
            Page<Order> page = new Page<>(current, size);
            QueryWrapper<Order> wrapper = new QueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like("recipient_name", keyword)
                       .or()
                       .like("recipient_phone", keyword);
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq("status", status);
            }

            // 排序
            if ("asc".equalsIgnoreCase(sortOrder)) {
                wrapper.orderByAsc(sortBy);
            } else {
                wrapper.orderByDesc(sortBy);
            }

            Page<Order> result = orderMapper.selectPage(page, wrapper);

            return Result.success(new PageResult<>(
                result.getRecords(),
                result.getTotal(),
                result.getCurrent(),
                result.getSize()
            ));
        } catch (Exception e) {
            return Result.error("获取订单列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/users/{id}/status")
    public Result<String> updateUserStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            user.setStatus(status);
            user.setUpdatedAt(LocalDateTime.now());
            userMapper.updateById(user);

            String statusText = status == 1 ? "启用" : "禁用";
            return Result.success("用户状态已更新为" + statusText);
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return Result.error("更新用户状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            userMapper.deleteById(id);
            return Result.success("用户删除成功");
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error("删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/users/batch")
    public Result<String> batchDeleteUsers(@RequestBody Map<String, List<Long>> params) {
        try {
            List<Long> ids = params.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.paramError("用户ID列表不能为空");
            }

            int deletedCount = userMapper.deleteBatchIds(ids);
            return Result.success("成功删除 " + deletedCount + " 个用户");
        } catch (Exception e) {
            log.error("批量删除用户失败", e);
            return Result.error("批量删除用户失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类列表
     */
    @GetMapping("/categories")
    public Result<PageResult<Category>> getCategories(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status) {
        try {
            Page<Category> page = new Page<>(current, size);
            LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like(Category::getName, keyword)
                       .or()
                       .like(Category::getDescription, keyword);
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq(Category::getStatus, status);
            }

            // 按排序顺序排序
            wrapper.orderByAsc(Category::getSortOrder);

            Page<Category> result = categoryMapper.selectPage(page, wrapper);

            return Result.success(new PageResult<>(
                result.getRecords(),
                result.getTotal(),
                result.getCurrent(),
                result.getSize()
            ));
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return Result.error("获取分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建分类
     */
    @PostMapping("/categories")
    public Result<Category> createCategory(@RequestBody Category category) {
        try {
            // 验证必填字段
            if (category.getName() == null || category.getName().trim().isEmpty()) {
                return Result.paramError("分类名称不能为空");
            }

            // 检查名称是否重复
            LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Category::getName, category.getName().trim());
            Category existing = categoryMapper.selectOne(wrapper);
            if (existing != null) {
                return Result.paramError("分类名称已存在");
            }

            // 设置默认值
            category.setName(category.getName().trim());
            if (category.getStatus() == null) {
                category.setStatus(1);
            }
            if (category.getSortOrder() == null) {
                category.setSortOrder(0);
            }
            category.setCreatedAt(LocalDateTime.now());
            category.setUpdatedAt(LocalDateTime.now());

            categoryMapper.insert(category);
            return Result.success("分类创建成功", category);
        } catch (Exception e) {
            log.error("创建分类失败", e);
            return Result.error("创建分类失败: " + e.getMessage());
        }
    }

    /**
     * 更新分类
     */
    @PutMapping("/categories/{id}")
    public Result<Category> updateCategory(@PathVariable Long id, @RequestBody Category category) {
        try {
            Category existing = categoryMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("分类不存在");
            }

            // 验证必填字段
            if (category.getName() == null || category.getName().trim().isEmpty()) {
                return Result.paramError("分类名称不能为空");
            }

            // 检查名称是否重复（排除自己）
            LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Category::getName, category.getName().trim())
                   .ne(Category::getId, id);
            Category duplicate = categoryMapper.selectOne(wrapper);
            if (duplicate != null) {
                return Result.paramError("分类名称已存在");
            }

            // 更新字段
            existing.setName(category.getName().trim());
            if (category.getDescription() != null) {
                existing.setDescription(category.getDescription());
            }
            if (category.getStatus() != null) {
                existing.setStatus(category.getStatus());
            }
            if (category.getSortOrder() != null) {
                existing.setSortOrder(category.getSortOrder());
            }
            existing.setUpdatedAt(LocalDateTime.now());

            categoryMapper.updateById(existing);
            return Result.success("分类更新成功", existing);
        } catch (Exception e) {
            log.error("更新分类失败", e);
            return Result.error("更新分类失败: " + e.getMessage());
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/categories/{id}")
    public Result<String> deleteCategory(@PathVariable Long id) {
        try {
            Category category = categoryMapper.selectById(id);
            if (category == null) {
                return Result.notFound("分类不存在");
            }

            // 检查是否有商品使用此分类
            LambdaQueryWrapper<Flower> flowerWrapper = new LambdaQueryWrapper<>();
            flowerWrapper.eq(Flower::getCategoryId, id);
            Long flowerCount = flowerMapper.selectCount(flowerWrapper);
            if (flowerCount > 0) {
                return Result.error("该分类下还有 " + flowerCount + " 个商品，无法删除");
            }

            categoryMapper.deleteById(id);
            return Result.success("分类删除成功");
        } catch (Exception e) {
            log.error("删除分类失败", e);
            return Result.error("删除分类失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品列表
     */
    @GetMapping("/flowers")
    public Result<PageResult<Flower>> getFlowers(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Integer status) {
        try {
            Page<Flower> page = new Page<>(current, size);
            LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                wrapper.like(Flower::getName, keyword)
                       .or()
                       .like(Flower::getDescription, keyword);
            }

            // 分类筛选
            if (categoryId != null) {
                wrapper.eq(Flower::getCategoryId, categoryId);
            }

            // 状态筛选
            if (status != null) {
                wrapper.eq(Flower::getStatus, status);
            }

            // 按创建时间倒序
            wrapper.orderByDesc(Flower::getCreatedAt);

            Page<Flower> result = flowerMapper.selectPage(page, wrapper);

            return Result.success(new PageResult<>(
                result.getRecords(),
                result.getTotal(),
                result.getCurrent(),
                result.getSize()
            ));
        } catch (Exception e) {
            log.error("获取商品列表失败", e);
            return Result.error("获取商品列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品详情
     */
    @GetMapping("/flowers/{id}")
    public Result<Flower> getFlowerDetail(@PathVariable Long id) {
        try {
            Flower flower = flowerMapper.selectById(id);
            if (flower == null) {
                return Result.notFound("商品不存在");
            }
            return Result.success(flower);
        } catch (Exception e) {
            log.error("获取商品详情失败", e);
            return Result.error("获取商品详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建商品
     */
    @PostMapping("/flowers")
    public Result<Flower> createFlower(@RequestBody Flower flower) {
        try {
            // 验证必填字段
            if (flower.getName() == null || flower.getName().trim().isEmpty()) {
                return Result.paramError("商品名称不能为空");
            }
            if (flower.getCategoryId() == null) {
                return Result.paramError("商品分类不能为空");
            }
            if (flower.getPrice() == null || flower.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                return Result.paramError("商品价格必须大于0");
            }

            // 验证分类是否存在
            Category category = categoryMapper.selectById(flower.getCategoryId());
            if (category == null) {
                return Result.paramError("商品分类不存在");
            }

            // 设置默认值
            flower.setName(flower.getName().trim());
            if (flower.getStatus() == null) {
                flower.setStatus(1);
            }
            if (flower.getStockQuantity() == null) {
                flower.setStockQuantity(0);
            }
            if (flower.getSalesCount() == null) {
                flower.setSalesCount(0);
            }
            flower.setCreatedAt(LocalDateTime.now());
            flower.setUpdatedAt(LocalDateTime.now());

            flowerMapper.insert(flower);
            return Result.success("商品创建成功", flower);
        } catch (Exception e) {
            log.error("创建商品失败", e);
            return Result.error("创建商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新商品
     */
    @PutMapping("/flowers/{id}")
    public Result<Flower> updateFlower(@PathVariable Long id, @RequestBody Flower flower) {
        try {
            Flower existing = flowerMapper.selectById(id);
            if (existing == null) {
                return Result.notFound("商品不存在");
            }

            // 验证必填字段
            if (flower.getName() != null && flower.getName().trim().isEmpty()) {
                return Result.paramError("商品名称不能为空");
            }
            if (flower.getPrice() != null && flower.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                return Result.paramError("商品价格必须大于0");
            }

            // 验证分类是否存在
            if (flower.getCategoryId() != null) {
                Category category = categoryMapper.selectById(flower.getCategoryId());
                if (category == null) {
                    return Result.paramError("商品分类不存在");
                }
                existing.setCategoryId(flower.getCategoryId());
            }

            // 更新字段
            if (flower.getName() != null) {
                existing.setName(flower.getName().trim());
            }
            if (flower.getDescription() != null) {
                existing.setDescription(flower.getDescription());
            }
            if (flower.getPrice() != null) {
                existing.setPrice(flower.getPrice());
            }
            if (flower.getOriginalPrice() != null) {
                existing.setOriginalPrice(flower.getOriginalPrice());
            }
            if (flower.getStockQuantity() != null) {
                existing.setStockQuantity(flower.getStockQuantity());
            }
            if (flower.getStatus() != null) {
                existing.setStatus(flower.getStatus());
            }
            if (flower.getMainImage() != null) {
                existing.setMainImage(flower.getMainImage());
            }
            existing.setUpdatedAt(LocalDateTime.now());

            flowerMapper.updateById(existing);
            return Result.success("商品更新成功", existing);
        } catch (Exception e) {
            log.error("更新商品失败", e);
            return Result.error("更新商品失败: " + e.getMessage());
        }
    }

    /**
     * 更新商品状态
     */
    @PutMapping("/flowers/{id}/status")
    public Result<String> updateFlowerStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            Flower flower = flowerMapper.selectById(id);
            if (flower == null) {
                return Result.notFound("商品不存在");
            }

            flower.setStatus(status);
            flower.setUpdatedAt(LocalDateTime.now());
            flowerMapper.updateById(flower);

            String statusText = status == 1 ? "上架" : "下架";
            return Result.success("商品已" + statusText);
        } catch (Exception e) {
            log.error("更新商品状态失败", e);
            return Result.error("更新商品状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/flowers/{id}")
    public Result<String> deleteFlower(@PathVariable Long id) {
        try {
            Flower flower = flowerMapper.selectById(id);
            if (flower == null) {
                return Result.notFound("商品不存在");
            }

            flowerMapper.deleteById(id);
            return Result.success("商品删除成功");
        } catch (Exception e) {
            log.error("删除商品失败", e);
            return Result.error("删除商品失败: " + e.getMessage());
        }
    }

    /**
     * 获取评价列表
     */
    @GetMapping("/reviews")
    public Result<PageResult<Map<String, Object>>> getReviews(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer rating) {
        try {
            Page<FlowerReview> page = new Page<>(current, size);
            LambdaQueryWrapper<FlowerReview> wrapper = new LambdaQueryWrapper<>();

            // 状态筛选
            if (status != null) {
                wrapper.eq(FlowerReview::getStatus, status);
            }

            // 评分筛选
            if (rating != null) {
                wrapper.eq(FlowerReview::getRating, rating);
            }

            // 按创建时间倒序
            wrapper.orderByDesc(FlowerReview::getCreatedAt);

            Page<FlowerReview> reviewPage = flowerReviewMapper.selectPage(page, wrapper);

            // 组装返回数据，包含用户和商品信息
            List<Map<String, Object>> reviewList = new ArrayList<>();
            for (FlowerReview review : reviewPage.getRecords()) {
                Map<String, Object> reviewMap = new HashMap<>();
                reviewMap.put("id", review.getId());
                reviewMap.put("userId", review.getUserId());
                reviewMap.put("flowerId", review.getFlowerId());
                reviewMap.put("orderId", review.getOrderId());
                reviewMap.put("rating", review.getRating());
                reviewMap.put("content", review.getContent());
                reviewMap.put("images", review.getImages());
                reviewMap.put("status", review.getStatus());
                reviewMap.put("createdAt", review.getCreatedAt());

                // 获取用户信息
                User user = userMapper.selectById(review.getUserId());
                if (user != null) {
                    reviewMap.put("userName", user.getNickname() != null ? user.getNickname() : "匿名用户");
                    reviewMap.put("userAvatar", user.getAvatarUrl());
                } else {
                    reviewMap.put("userName", "匿名用户");
                    reviewMap.put("userAvatar", "");
                }

                // 获取商品信息
                Flower flower = flowerMapper.selectById(review.getFlowerId());
                if (flower != null) {
                    reviewMap.put("flowerName", flower.getName());
                } else {
                    reviewMap.put("flowerName", "商品已删除");
                }

                // 关键词搜索过滤
                if (keyword != null && !keyword.trim().isEmpty()) {
                    String searchKeyword = keyword.toLowerCase();
                    String userName = (String) reviewMap.get("userName");
                    String flowerName = (String) reviewMap.get("flowerName");
                    String content = review.getContent();

                    if ((userName != null && userName.toLowerCase().contains(searchKeyword)) ||
                        (flowerName != null && flowerName.toLowerCase().contains(searchKeyword)) ||
                        (content != null && content.toLowerCase().contains(searchKeyword))) {
                        reviewList.add(reviewMap);
                    }
                } else {
                    reviewList.add(reviewMap);
                }
            }

            return Result.success(new PageResult<>(
                reviewList,
                reviewPage.getTotal(),
                reviewPage.getCurrent(),
                reviewPage.getSize()
            ));
        } catch (Exception e) {
            log.error("获取评价列表失败", e);
            return Result.error("获取评价列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新评价状态
     */
    @PutMapping("/reviews/{id}/status")
    public Result<String> updateReviewStatus(@PathVariable Long id, @RequestBody Map<String, Integer> params) {
        try {
            Integer status = params.get("status");
            if (status == null) {
                return Result.paramError("状态参数不能为空");
            }

            FlowerReview review = flowerReviewMapper.selectById(id);
            if (review == null) {
                return Result.notFound("评价不存在");
            }

            review.setStatus(status);
            review.setUpdatedAt(LocalDateTime.now());
            flowerReviewMapper.updateById(review);

            String statusText = status == 1 ? "通过" : "拒绝";
            return Result.success("评价审核" + statusText);
        } catch (Exception e) {
            log.error("更新评价状态失败", e);
            return Result.error("更新评价状态失败: " + e.getMessage());
        }
    }

    /**
     * 删除评价
     */
    @DeleteMapping("/reviews/{id}")
    public Result<String> deleteReview(@PathVariable Long id) {
        try {
            FlowerReview review = flowerReviewMapper.selectById(id);
            if (review == null) {
                return Result.notFound("评价不存在");
            }

            flowerReviewMapper.deleteById(id);
            return Result.success("评价删除成功");
        } catch (Exception e) {
            log.error("删除评价失败", e);
            return Result.error("删除评价失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除评价
     */
    @DeleteMapping("/reviews/batch")
    public Result<String> batchDeleteReviews(@RequestBody Map<String, List<Long>> params) {
        try {
            List<Long> ids = params.get("ids");
            if (ids == null || ids.isEmpty()) {
                return Result.paramError("评价ID列表不能为空");
            }

            int deletedCount = flowerReviewMapper.deleteBatchIds(ids);
            return Result.success("成功删除 " + deletedCount + " 条评价");
        } catch (Exception e) {
            log.error("批量删除评价失败", e);
            return Result.error("批量删除评价失败: " + e.getMessage());
        }
    }

    /**
     * 获取省份列表
     */
    @GetMapping("/provinces")
    public Result<List<Province>> getProvinces() {
        try {
            List<Province> provinces = regionMapper.getAllProvinces();
            return Result.success(provinces);
        } catch (Exception e) {
            log.error("获取省份列表失败", e);
            return Result.error("获取省份列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取城市列表
     */
    @GetMapping("/cities/{provinceCode}")
    public Result<List<City>> getCities(@PathVariable String provinceCode) {
        try {
            List<City> cities = regionMapper.getCitiesByProvinceCode(provinceCode);
            return Result.success(cities);
        } catch (Exception e) {
            log.error("获取城市列表失败", e);
            return Result.error("获取城市列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取区县列表
     */
    @GetMapping("/districts/{cityCode}")
    public Result<List<District>> getDistricts(@PathVariable String cityCode) {
        try {
            List<District> districts = regionMapper.getDistrictsByCityCode(cityCode);
            return Result.success(districts);
        } catch (Exception e) {
            log.error("获取区县列表失败", e);
            return Result.error("获取区县列表失败: " + e.getMessage());
        }
    }

    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
