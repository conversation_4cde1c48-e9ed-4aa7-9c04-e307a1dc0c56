package com.flower.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.flower.common.PageResult;
import com.flower.entity.*;
import com.flower.mapper.*;
import com.flower.service.OrderService;
import com.flower.service.UserPickupInfoService;
import com.flower.entity.UserPickupInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 订单服务实现类
 */
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderItemMapper orderItemMapper;

    @Autowired
    private CartItemMapper cartItemMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserPickupInfoService userPickupInfoService;

    @Override
    @Transactional
    public Order createOrderFromCart(Long userId, String recipientName, String recipientPhone, 
                                   String recipientAddress, String deliveryNotes, String remark) {
        // 获取购物车商品
        LambdaQueryWrapper<CartItem> cartWrapper = new LambdaQueryWrapper<>();
        cartWrapper.eq(CartItem::getUserId, userId);
        List<CartItem> cartItems = cartItemMapper.selectList(cartWrapper);

        if (cartItems.isEmpty()) {
            throw new RuntimeException("购物车为空");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setStatus(1); // 待付款
        order.setPaymentStatus(0); // 未支付
        order.setRecipientName(recipientName);
        order.setRecipientPhone(recipientPhone);
        order.setRecipientAddress(recipientAddress);
        order.setDeliveryNotes(deliveryNotes);
        order.setRemark(remark);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        // 计算订单总金额
        for (CartItem cartItem : cartItems) {
            Flower flower = flowerMapper.selectById(cartItem.getFlowerId());
            if (flower != null && flower.getStatus() == 1) {
                BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(cartItem.getQuantity()));
                totalAmount = totalAmount.add(subtotal);
            }
        }

        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setFinalAmount(totalAmount);

        orderMapper.insert(order);

        // 创建订单商品
        for (CartItem cartItem : cartItems) {
            Flower flower = flowerMapper.selectById(cartItem.getFlowerId());
            if (flower != null && flower.getStatus() == 1) {
                OrderItem orderItem = new OrderItem();
                orderItem.setOrderId(order.getId());
                orderItem.setFlowerId(flower.getId());
                orderItem.setFlowerName(flower.getName());
                orderItem.setFlowerImage(flower.getMainImage());
                orderItem.setPrice(flower.getPrice());
                orderItem.setQuantity(cartItem.getQuantity());
                orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(cartItem.getQuantity())));
                orderItem.setCreatedAt(LocalDateTime.now());

                orderItemMapper.insert(orderItem);
            }
        }

        // 清空购物车
        cartItemMapper.delete(cartWrapper);
        
        return order;
    }

    @Override
    @Transactional
    public Order createOrder(Long userId, List<Long> flowerIds, List<Integer> quantities,
                           String recipientName, String recipientPhone, String recipientAddress, 
                           String deliveryNotes, String remark) {
        if (flowerIds.size() != quantities.size()) {
            throw new RuntimeException("Flower IDs and quantities size mismatch");
        }
        
        // Create order
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setStatus(1); // pending
        order.setPaymentStatus(0); // unpaid
        order.setRecipientName(recipientName);
        order.setRecipientPhone(recipientPhone);
        order.setRecipientAddress(recipientAddress);
        order.setDeliveryNotes(deliveryNotes);
        order.setRemark(remark);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        // Calculate total amount
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower != null && flower.getStatus() == 1) {
                BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(quantities.get(i)));
                totalAmount = totalAmount.add(subtotal);
            }
        }
        
        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setFinalAmount(totalAmount);
        
        orderMapper.insert(order);
        
        // Create order items
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower != null && flower.getStatus() == 1) {
                OrderItem orderItem = new OrderItem();
                orderItem.setOrderId(order.getId());
                orderItem.setFlowerId(flower.getId());
                orderItem.setFlowerName(flower.getName());
                orderItem.setFlowerImage(flower.getMainImage());
                orderItem.setPrice(flower.getPrice());
                orderItem.setQuantity(quantities.get(i));
                orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(quantities.get(i))));
                orderItem.setCreatedAt(LocalDateTime.now());
                
                orderItemMapper.insert(orderItem);
            }
        }
        
        return order;
    }

    @Override
    public Order getOrderById(Long orderId) {
        return orderMapper.selectById(orderId);
    }

    @Override
    public List<OrderItem> getOrderItems(Long orderId) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItem::getOrderId, orderId);
        return orderItemMapper.selectList(wrapper);
    }

    @Override
    public PageResult<Order> getUserOrders(Long userId, Long current, Long size, Integer status) {
        Page<Order> page = new Page<>(current, size);
        LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
        
        wrapper.eq(Order::getUserId, userId);
        if (status != null) {
            wrapper.eq(Order::getStatus, status);
        }
        wrapper.orderByDesc(Order::getCreatedAt);
        
        IPage<Order> result = orderMapper.selectPage(page, wrapper);
        return PageResult.of(result.getRecords(), result.getTotal(), result.getSize(), result.getCurrent());
    }

    @Override
    public Order updateOrderStatus(Long orderId, Integer status) {
        Order order = orderMapper.selectById(orderId);
        if (order != null) {
            order.setStatus(status);
            order.setUpdatedAt(LocalDateTime.now());
            
            if (status == 3) { // shipped
                order.setShippedAt(LocalDateTime.now());
            } else if (status == 4) { // delivered
                order.setDeliveredAt(LocalDateTime.now());
            }
            
            orderMapper.updateById(order);
        }
        return order;
    }

    @Override
    public Boolean cancelOrder(Long orderId, Long userId) {
        Order order = orderMapper.selectById(orderId);
        if (order != null && order.getUserId().equals(userId) && order.getStatus() == 1) {
            order.setStatus(5); // cancelled
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.updateById(order);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean payOrder(Long orderId, String paymentMethod) {
        Order order = orderMapper.selectById(orderId);
        if (order != null && order.getStatus() == 1 && order.getPaymentStatus() == 0) {
            order.setStatus(2); // paid
            order.setPaymentStatus(1); // paid
            order.setPaymentMethod(paymentMethod);
            order.setPaidAt(LocalDateTime.now());
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.updateById(order);
            
            // Update flower sales count
            List<OrderItem> orderItems = getOrderItems(orderId);
            for (OrderItem item : orderItems) {
                Flower flower = flowerMapper.selectById(item.getFlowerId());
                if (flower != null) {
                    flower.setSalesCount(flower.getSalesCount() + item.getQuantity());
                    flower.setStockQuantity(flower.getStockQuantity() - item.getQuantity());
                    flowerMapper.updateById(flower);
                }
            }
            
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Order createDirectOrder(Long userId, List<Long> flowerIds, List<Integer> quantities,
                                  Integer deliveryType, String recipientName, String recipientPhone,
                                  String recipientAddress, String deliveryTime, String pickupName, String pickupPhone,
                                  String pickupTime, String remark, String backupAddress) {
        if (flowerIds.size() != quantities.size()) {
            throw new RuntimeException("商品ID和数量不匹配");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setStatus(2); // 直接设为已下单状态（跳过支付）
        order.setPaymentStatus(1); // 设为已支付（无需支付）
        order.setPaymentMethod("无需支付");
        order.setDeliveryType(deliveryType);
        order.setRemark(remark);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        order.setPaidAt(LocalDateTime.now());

        // 根据配送方式设置相关信息
        if (deliveryType == 1) {
            // 外卖配送
            order.setRecipientName(recipientName);
            order.setRecipientPhone(recipientPhone);
            order.setRecipientAddress(recipientAddress);
            order.setDeliveryTime(deliveryTime);
        } else {
            // 自取
            order.setPickupName(pickupName);
            order.setPickupPhone(pickupPhone);
            order.setPickupTime(pickupTime);
            if (backupAddress != null && !backupAddress.trim().isEmpty()) {
                order.setRecipientAddress(backupAddress); // 备用地址存储在收货地址字段
            }

            // 保存用户自取信息
            UserPickupInfo pickupInfo = new UserPickupInfo();
            pickupInfo.setUserId(userId);
            pickupInfo.setPickupName(pickupName);
            pickupInfo.setPickupPhone(pickupPhone);
            pickupInfo.setPickupTime(pickupTime);
            pickupInfo.setRemark(remark);
            pickupInfo.setBackupAddress(backupAddress);
            userPickupInfoService.saveOrUpdate(pickupInfo);
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        // 计算订单总金额并验证商品
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower == null || flower.getStatus() != 1) {
                throw new RuntimeException("商品不存在或已下架");
            }
            if (flower.getStockQuantity() < quantities.get(i)) {
                throw new RuntimeException("商品库存不足：" + flower.getName());
            }
            BigDecimal subtotal = flower.getPrice().multiply(new BigDecimal(quantities.get(i)));
            totalAmount = totalAmount.add(subtotal);
        }

        order.setTotalAmount(totalAmount);
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setFinalAmount(totalAmount);

        orderMapper.insert(order);

        // 创建订单商品并更新库存
        for (int i = 0; i < flowerIds.size(); i++) {
            Flower flower = flowerMapper.selectById(flowerIds.get(i));
            if (flower != null && flower.getStatus() == 1) {
                OrderItem orderItem = new OrderItem();
                orderItem.setOrderId(order.getId());
                orderItem.setFlowerId(flower.getId());
                orderItem.setFlowerName(flower.getName());
                orderItem.setFlowerImage(flower.getMainImage());
                orderItem.setPrice(flower.getPrice());
                orderItem.setQuantity(quantities.get(i));
                orderItem.setSubtotal(flower.getPrice().multiply(new BigDecimal(quantities.get(i))));
                orderItem.setCreatedAt(LocalDateTime.now());

                orderItemMapper.insert(orderItem);

                // 更新商品销量和库存
                flower.setSalesCount(flower.getSalesCount() + quantities.get(i));
                flower.setStockQuantity(flower.getStockQuantity() - quantities.get(i));
                flowerMapper.updateById(flower);
            }
        }

        return order;
    }

    private String generateOrderNo() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = IdUtil.randomUUID().substring(0, 6).toUpperCase();
        return "FL" + timestamp + random;
    }
}
