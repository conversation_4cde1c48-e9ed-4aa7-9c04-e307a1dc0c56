package com.flower.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.flower.entity.UserFavorite;
import com.flower.entity.Flower;
import com.flower.mapper.UserFavoriteMapper;
import com.flower.mapper.FlowerMapper;
import com.flower.mapper.UserMapper;
import com.flower.service.FavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收藏服务实现类
 */
@Service
public class FavoriteServiceImpl implements FavoriteService {

    @Autowired
    private UserFavoriteMapper userFavoriteMapper;

    @Autowired
    private FlowerMapper flowerMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public UserFavorite addToFavorites(Long userId, Long flowerId) {
        try {
            // 检查参数
            if (userId == null || flowerId == null) {
                throw new IllegalArgumentException("用户ID和花卉ID不能为空");
            }

            // 检查用户是否存在
            if (userMapper.selectById(userId) == null) {
                throw new IllegalArgumentException("用户不存在，用户ID: " + userId);
            }

            // 检查花卉是否存在
            if (flowerMapper.selectById(flowerId) == null) {
                throw new IllegalArgumentException("花卉不存在，花卉ID: " + flowerId);
            }

            // 检查是否已存在
            if (isFavorite(userId, flowerId)) {
                return null;
            }

            UserFavorite favorite = new UserFavorite();
            favorite.setUserId(userId);
            favorite.setFlowerId(flowerId);
            favorite.setCreatedAt(LocalDateTime.now());

            int result = userFavoriteMapper.insert(favorite);
            if (result > 0) {
                return favorite;
            } else {
                throw new RuntimeException("插入收藏记录失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("添加收藏失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean removeFromFavorites(Long userId, Long flowerId) {
        try {
            // 检查参数
            if (userId == null || flowerId == null) {
                throw new IllegalArgumentException("用户ID和花卉ID不能为空");
            }

            LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserFavorite::getUserId, userId);
            wrapper.eq(UserFavorite::getFlowerId, flowerId);

            int result = userFavoriteMapper.delete(wrapper);
            return result > 0;
        } catch (Exception e) {
            throw new RuntimeException("取消收藏失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Boolean isFavorite(Long userId, Long flowerId) {
        try {
            // 检查参数
            if (userId == null || flowerId == null) {
                return false;
            }

            LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserFavorite::getUserId, userId);
            wrapper.eq(UserFavorite::getFlowerId, flowerId);

            UserFavorite favorite = userFavoriteMapper.selectOne(wrapper);
            return favorite != null;
        } catch (Exception e) {
            // 查询失败时返回false，不抛出异常
            return false;
        }
    }

    @Override
    public Boolean toggleFavorite(Long userId, Long flowerId) {
        if (isFavorite(userId, flowerId)) {
            removeFromFavorites(userId, flowerId);
            return false;
        } else {
            addToFavorites(userId, flowerId);
            return true;
        }
    }

    @Override
    public List<UserFavorite> getUserFavorites(Long userId) {
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId);
        wrapper.orderByDesc(UserFavorite::getCreatedAt);

        return userFavoriteMapper.selectList(wrapper);
    }

    @Override
    public List<Flower> getUserFavoriteFlowers(Long userId) {
        List<UserFavorite> favorites = getUserFavorites(userId);

        if (favorites.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> flowerIds = favorites.stream()
                .map(UserFavorite::getFlowerId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<Flower> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Flower::getId, flowerIds);
        wrapper.eq(Flower::getStatus, 1); // 只返回上架的商品

        List<Flower> flowers = flowerMapper.selectList(wrapper);

        // 按收藏时间排序
        return flowers.stream()
                .sorted((f1, f2) -> {
                    UserFavorite fav1 = favorites.stream()
                            .filter(fav -> fav.getFlowerId().equals(f1.getId()))
                            .findFirst().orElse(null);
                    UserFavorite fav2 = favorites.stream()
                            .filter(fav -> fav.getFlowerId().equals(f2.getId()))
                            .findFirst().orElse(null);

                    if (fav1 == null || fav2 == null) return 0;
                    return fav2.getCreatedAt().compareTo(fav1.getCreatedAt());
                })
                .collect(Collectors.toList());
    }

    @Override
    public Integer getFavoriteCount(Long userId) {
        LambdaQueryWrapper<UserFavorite> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserFavorite::getUserId, userId);

        return Math.toIntExact(userFavoriteMapper.selectCount(wrapper));
    }
}
