package com.flower.controller;

import com.flower.common.Result;
import com.flower.entity.User;
import com.flower.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import com.flower.util.WeChatDecryptUtil;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@CrossOrigin(origins = "*")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 微信登录
     */
    @PostMapping("/login")
    public Result<User> login(@RequestBody Map<String, String> params) {
        try {
            String code = params.get("code");
            if (code == null || code.trim().isEmpty()) {
                return Result.paramError("登录凭证不能为空");
            }

            User user = userService.wechatLogin(code);
            return Result.success("登录成功", user);
        } catch (Exception e) {
            log.error("登录失败", e);
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info/{openid}")
    public Result<User> getUserInfo(@PathVariable String openid) {
        try {
            User user = userService.getUserByOpenid(openid);
            if (user == null) {
                return Result.notFound("用户不存在");
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/update")
    public Result<User> updateUser(@RequestBody User user) {
        try {
            if (user.getId() == null) {
                return Result.paramError("用户ID不能为空");
            }

            User updatedUser = userService.updateUser(user);
            return Result.success("用户信息更新成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败");
        }
    }

    /**
     * 上传用户头像
     */
    @PostMapping("/upload-avatar")
    public Result<Map<String, String>> uploadAvatar(@RequestParam("file") MultipartFile file,
                                                   @RequestParam("userId") Long userId) {
        try {
            if (file.isEmpty()) {
                return Result.paramError("头像文件不能为空");
            }

            if (userId == null) {
                return Result.paramError("用户ID不能为空");
            }

            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.paramError("只支持图片格式的头像");
            }

            // 验证文件大小（限制为5MB）
            if (file.getSize() > 5 * 1024 * 1024) {
                return Result.paramError("头像文件大小不能超过5MB");
            }

            String avatarUrl = userService.uploadAvatar(file, userId);

            Map<String, String> result = new HashMap<>();
            result.put("avatarUrl", avatarUrl);

            return Result.success("头像上传成功", result);
        } catch (Exception e) {
            log.error("头像上传失败", e);
            return Result.error("头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户头像和昵称（登录时使用）
     */
    @PostMapping("/update-profile")
    public Result<User> updateUserProfile(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            String nickname = (String) params.get("nickname");
            String avatarBase64 = (String) params.get("avatarBase64");
            String phone = (String) params.get("phone");

            if (userId == null) {
                return Result.paramError("用户ID不能为空");
            }

            User updatedUser = userService.updateUserProfile(userId, nickname, avatarBase64, phone);
            return Result.success("用户信息更新成功", updatedUser);
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return Result.error("更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户手机号
     */
    @PutMapping("/phone")
    public Result<User> updatePhone(@RequestBody Map<String, Object> params) {
        try {
            Long userId = Long.valueOf(params.get("userId").toString());
            String phone = params.get("phone").toString();

            if (phone == null || phone.trim().isEmpty()) {
                return Result.paramError("手机号不能为空");
            }

            User user = userService.updateUserPhone(userId, phone);
            return Result.success("手机号更新成功", user);
        } catch (Exception e) {
            log.error("更新手机号失败", e);
            return Result.error("更新手机号失败");
        }
    }

    /**
     * 解密手机号接口
     */
    @PostMapping("/phone")
    public Result<Map<String, Object>> decryptPhone(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            String encryptedData = (String) request.get("encryptedData");
            String iv = (String) request.get("iv");

            log.info("开始解密手机号，userId: {}", userId);

            // 获取用户信息，包含sessionKey
            User user = userService.findById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            String sessionKey = user.getSessionKey();

            // 检查是否为模拟请求（开发环境）
            if ("mock_encrypted_data".equals(encryptedData) || "mock_iv".equals(iv) ||
                sessionKey == null || sessionKey.isEmpty() || sessionKey.startsWith("mock_")) {
                log.warn("检测到模拟请求或sessionKey为空，使用模拟数据，userId: {}", userId);
                return generateMockPhoneResult(user);
            }

            try {
                // 使用真实的解密算法
                JSONObject phoneInfo = WeChatDecryptUtil.decryptPhoneNumber(encryptedData, sessionKey, iv);

                Map<String, Object> result = new HashMap<>();
                result.put("phoneNumber", phoneInfo.getString("phoneNumber"));
                result.put("purePhoneNumber", phoneInfo.getString("purePhoneNumber"));
                result.put("countryCode", phoneInfo.getString("countryCode"));

                // 更新用户手机号
                user.setPhone(phoneInfo.getString("purePhoneNumber"));
                userService.save(user);

                log.info("用户手机号解密并更新成功，userId: {}, phone: {}", userId, phoneInfo.getString("purePhoneNumber"));
                return Result.success("手机号获取成功", result);

            } catch (Exception decryptError) {
                log.error("手机号解密失败，使用模拟数据，userId: {}", userId, decryptError);
                return generateMockPhoneResult(user);
            }

        } catch (Exception e) {
            log.error("解密手机号失败", e);
            return Result.error("解密手机号失败: " + e.getMessage());
        }
    }

    /**
     * 检查配置信息（调试用）
     */
    @GetMapping("/config")
    public Result<String> checkConfig() {
        return Result.success("配置检查完成");
    }

    /**
     * 生成模拟手机号结果
     */
    private Result<Map<String, Object>> generateMockPhoneResult(User user) {
        // 生成模拟手机号
        String[] prefixes = {"138", "139", "150", "151", "152", "158", "159", "188", "189"};
        String prefix = prefixes[(int)(Math.random() * prefixes.length)];
        String mockPhone = prefix + String.format("%08d", (int)(Math.random() * 100000000));

        Map<String, Object> mockResult = new HashMap<>();
        mockResult.put("phoneNumber", mockPhone.substring(0, 3) + "****" + mockPhone.substring(7));
        mockResult.put("purePhoneNumber", mockPhone);
        mockResult.put("countryCode", "86");

        // 更新用户手机号
        user.setPhone(mockPhone);
        userService.save(user);

        log.info("生成模拟手机号: {}", mockPhone);
        return Result.success("手机号获取成功（开发环境模拟）", mockResult);
    }

    /**
     * 解密手机号（用于编辑页面）
     */
    @PostMapping("/decrypt-phone")
    public Result<Map<String, String>> decryptPhoneForEdit(@RequestBody Map<String, String> params) {
        try {
            String userId = params.get("userId");
            String encryptedData = params.get("encryptedData");
            String iv = params.get("iv");

            if (userId == null || encryptedData == null || iv == null) {
                return Result.paramError("参数不完整");
            }

            // 获取用户信息
            User user = userService.findById(Long.parseLong(userId));
            if (user == null) {
                return Result.notFound("用户不存在");
            }

            // 模拟解密手机号（实际项目中需要调用微信API解密）
            String[] prefixes = {"138", "139", "150", "151", "152", "158", "159", "188", "189"};
            String prefix = prefixes[(int)(Math.random() * prefixes.length)];
            String mockPhone = prefix + String.format("%08d", (int)(Math.random() * 100000000));

            // 更新用户手机号
            user.setPhone(mockPhone);
            userService.save(user);

            Map<String, String> result = new HashMap<>();
            result.put("phone", mockPhone);

            log.info("用户 {} 绑定手机号: {}", userId, mockPhone);
            return Result.success("手机号绑定成功", result);
        } catch (Exception e) {
            log.error("解密手机号失败", e);
            return Result.error("手机号绑定失败");
        }
    }
}
