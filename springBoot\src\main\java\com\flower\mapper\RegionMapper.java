package com.flower.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.flower.entity.Province;
import com.flower.entity.City;
import com.flower.entity.District;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 地区数据Mapper
 */
@Mapper
public interface RegionMapper extends BaseMapper<Province> {
    
    /**
     * 获取所有省份
     */
    @Select("SELECT * FROM provinces ORDER BY code")
    List<Province> getAllProvinces();
    
    /**
     * 根据省份代码获取城市列表
     */
    @Select("SELECT * FROM cities WHERE province_code = #{provinceCode} ORDER BY code")
    List<City> getCitiesByProvinceCode(String provinceCode);
    
    /**
     * 根据城市代码获取区县列表
     */
    @Select("SELECT * FROM districts WHERE city_code = #{cityCode} ORDER BY code")
    List<District> getDistrictsByCityCode(String cityCode);
    
    /**
     * 根据省份名称获取省份信息
     */
    @Select("SELECT * FROM provinces WHERE name = #{provinceName}")
    Province getProvinceByName(String provinceName);
    
    /**
     * 根据城市名称和省份代码获取城市信息
     */
    @Select("SELECT * FROM cities WHERE name = #{cityName} AND province_code = #{provinceCode}")
    City getCityByNameAndProvinceCode(String cityName, String provinceCode);
    
    /**
     * 根据区县名称和城市代码获取区县信息
     */
    @Select("SELECT * FROM districts WHERE name = #{districtName} AND city_code = #{cityCode}")
    District getDistrictByNameAndCityCode(String districtName, String cityCode);
}
